#服务器配置
server:
  port: 8001
  undertow:
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 400
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    #driver-class-name: org.postgresql.Driver
    #driver-class-name: oracle.jdbc.OracleDriver
    #driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      # MySql、PostgreSQL、SqlServer校验
      validation-query: select 1
      # Oracle校验
      #validation-query: select 1 from dual
      validation-query-timeout: 2000
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      stat-view-servlet:
        enabled: true
        login-username: blade
        login-password: 1qaz@WSX
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: '*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*'
        session-stat-enable: true
        session-stat-max-count: 10

# mybatis
mybatis-plus:
  mapper-locations: classpath:org/springblade/**/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: org.springblade.**.entity
  #typeEnumsPackage: org.springblade.dashboard.entity.enums
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增", 1:"不操作", 2:"用户输入ID",3:"数字型snowflake", 4:"全局唯一ID UUID", 5:"字符串型snowflake";
      id-type: assign_id
      #字段策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_empty
      #驼峰下划线转换
      table-underline: true
      # 逻辑删除配置
      # 逻辑删除全局值（1表示已删除，这也是Mybatis Plus的默认配置）
      logic-delete-value: 1
      # 逻辑未删除全局值（0表示未删除，这也是Mybatis Plus的默认配置）
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

#knife4j配置
knife4j:
  #启用
  enable: true
  #基础认证
  basic:
    enable: false
    username: blade
    password: blade
  #增强配置
  setting:
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://localhost
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh-CN
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2021 BladeX All Rights Reserved

#swagger公共信息
swagger:
  title: BladeX 接口文档系统
  description: BladeX 接口文档系统
  version: 2.9.0.RELEASE
  license: Powered By BladeX
  license-url: https://bladex.vip
  terms-of-service-url: https://bladex.vip
  contact:
    name: smallchill
    email: <EMAIL>
    url: https://gitee.com/smallc
#
##flowable配置
flowable:
  activity-font-name: \u5B8B\u4F53
  label-font-name: \u5B8B\u4F53
  annotation-font-name: \u5B8B\u4F53
  check-process-definitions: false
  database-schema-update: false # 表⾃动更新
  async-executor-activate: false # 关闭定时任务
  app:
    enabled: false
  cmmn:
    enabled: false
  idm:
    enabled: false
  dmn:
    enabled: false
  form:
    enabled: false
  content:
    enabled: false
  eventregistry:
    enabled: false
  custom-mybatis-x-m-l-mappers:
    - org/springblade/workflow/process/mapper/WfHistoricTaskInstance.xml
    - org/springblade/workflow/process/mapper/WfProcessDefinition.xml

#报表配置
report:
  enabled: true
  database:
    provider:
      prefix: blade-

#oss默认配置
oss:
  enabled: true
  name: qiniu
  tenant-mode: true
  endpoint: http://prt1thnw3.bkt.clouddn.com
  access-key: N_Loh1ngBqcJovwiAJqR91Ifj2vgOWHOf8AwBA_h
  secret-key: AuzuA1KHAbkIndCU0dB3Zfii2O3crHNODDmpxHRS
  bucket-name: bladex

#第三方登陆配置
social:
  oauth:
    GITHUB:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/github
    GITEE:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/gitee
    WECHAT_OPEN:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/wechat
    QQ:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/qq
    DINGTALK:
      client-id: 233************
      client-secret: 233************************************
      redirect-uri: ${social.domain}/oauth/redirect/dingtalk
#blade配置
blade:
  #token配置
  token:
    #是否有状态
    state: false
  #redis序列化方式
  redis:
    serializer-type: ProtoStuff
  #接口配置
  api:
    #报文加密配置
    crypto:
      #启用报文加密配置
      enabled: true
      #使用AesUtil.genAesKey()生成
      aes-key: O2BEeIv399qHQNhD6aGW8R8DEj4bqHXm
      #使用DesUtil.genDesKey()生成
      des-key: jMVCBsFGDQr1USHo
  #jackson配置
  jackson:
    #null自动转空值
    null-to-empty: false
    #大数字自动转字符串
    big-num-to-string: true
    #支持text文本请求,与报文加密同时开启
    support-text-plain: true
  log:
    request:
      enabled: true
  mybatis-plus:
    sql-log: true
  #xss配置
  xss:
    enabled: true
    skip-url:
      - /blade-chat/weixin
      - /blade-desk/notice/submit
      - /blade-resource/qqMail/send
      - /goods/save
      - /goods/update
      - /blade-workflow/design/model/submit
      - /blade-workflow/design/form/submit
      - /blade-workflow/form/model/submit
      - /blade-workflow/process/startProcess
      - /blade-workflow/process/completeTask
      - /blade-help/web-back/consult/consultsystemconsultation/save
      - /blade-help/web-back/consult/consultsystemconsultation/update
      - /blade-drools/web-back/drools/conditioninfo/update
      - /blade-drools/web-back/drools/conditioninfo/submit
      - /blade-front/web-back/front/protocol/**
      - /blade-visual/**
  #安全框架配置
  secure:
    #接口放行
    skip-url:
      - /blade-test/**
      - /ureport/res/**
      - /blade-resource/**
      - /blade-riskmana/web-back/riskmana/ratingrecord/test
      - /asyncNotify/commmon
      - /blade-otherApi/web-back/authApi/endpoint/transfer/shortToLongUrl/**
      - /blade-customer/web-applet/front/customerFrontUserType/web/register
      - /blade-customer/web-applet/front/customer/send-validate
      - /jmreport/**
      - /blade-pay/web-back/combine_merge/signRegisterCallback
      - /blade-pay/web-back/combine_merge/signContractCallback
      - /blade-pay/web-back/pay_goods_order/buy
      - /blade-pay/web-back/pay_goods_order/wxPay
      - /blade-pay/web-back/pay_goods_order/aliPay
      - /blade-pay/web-back/notify/heLiBao/**
      - /blade-pay/web-back/notify/addNotify/**
      - /blade-pay/web-back/notify/transfer/recharge/**
      - /blade-pay/web-back/notify/transfer/callback/**
      - /blade-pay/web-back/notify/transfer/settlement/callback/**
      - /blade-pay/web-back/notify/transfer/onlineRepaymentPayNotify/**
      - /blade-otherApi/web-back/authApi/endpoint/face/**
      - /blade-customer/web-front/customerGoods/listProduct
      - /blade-develop/curdtest/**
      - /blade-develop/curdtest/save
      - /blade-front/web-front/front/qualityProducts/**
      - /blade_feign/blade-user/dept/name
      - /blade-system/tenant/logo
      - /pay/external/pay-counter/pay/notify/**
      - /blade-customer/web-front/customerGoods/detailsProduct
      - /pay/external/pay-counter/v2/getQrCode/**
      - /bill/refund/notify/result/**
      - /riskmana/riskmanaApply/risk-order-two/notify/**
      - /blade_product/web-back/product/list-system-product-type
      - /blade-customer/web-front/multi-funding/product/get-multi-funding-product
      - /external/front/customerAccountConnection/accountConnection
      - /external/front/customerAccountConnection/orderPush
      - /external/front/customerAccountConnection/orderDel
      - /external/front/customerAccountConnection/getGoodsAndQuota
      - /blade-front/web-front/front/webIntroduce/getByType
      - /blade-goods/web-front/intermediation-consult/page
      - /blade-goods/web-front/intermediation-consult/detail
      - /blade-customer/web-front/multi-funding/product/getIntermediationProduct
      - /blade-customer/web-front/multi-funding/product/get-multi-funding-product-by-id
      - /blade-goods/web-front/intermediation-consult/qualityProduct
      - /blade-help/web-front/consult/consultsystemconsultation/help-center-data
      - /blade-help/web-front/consult/consultsystemconsultation/list
      - /blade-goods/web-front/wx/getParam
      #授权认证配置
    auth:
      -   method: ALL
          pattern: /blade-chat/weixin/**
          expression: "hasAuth()"
      -   method: POST
          pattern: /blade-desk/dashboard/upload
          expression: "hasTimeAuth(9, 17)"
      -   method: POST
          pattern: /blade-desk/dashboard/submit
          expression: "hasAnyRole('administrator', 'admin', 'user')"
    #基础认证配置
    basic:
      -   method: ALL
          pattern: /blade-desk/dashboard/info
          username: "blade"
          password: "blade"
    #动态签名认证配置
    sign:
      -   method: ALL
          pattern: /blade-desk/dashboard/sign
          crypto: "sha1"
    #多终端认证配置
    client:
      -   client-id: sword
          path-patterns:
            - /blade-sword/**
      -   client-id: saber
          path-patterns:
            - /blade-saber/**
  #多租户配置
  tenant:
    #多租户增强
    enhance: true
    #多租户授权保护
    license: false
    #动态数据源功能
    dynamic-datasource: false
    #动态数据源全局扫描
    dynamic-global: false
    #多租户字段名
    column: tenant_id
    #排除多租户逻辑
    exclude-tables:
jrzh:
  aesKey: weTH6Fe17J9teowbr1IKyA5nh4tmpE8w
  rsaPrivatePath: src/main/resources/jrzh.risk/jrzhRiskRSAPrivateKey.txt
  #是否开启boot远程调用
  feign:
    enabled: true
  fisco:
    enabled: false
wx:
  mp:
    app-id: wxc3cc29109b6117b3
    secret: ca24cd4c8cd83fb10a8acd916ff871eb
  chat:
    appid: wx45af3c8bd749306c
    appsecret: 7915b1c50b2e4583bcc6c3ec05f29c5c
    accessTokenUrl: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s
    jsapiTicketUrl: https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi

 #打印日志
logging:
  pattern:
    # 输出到控制台
    console: '%d{HH:mm:ss.SSS} %-5level %msg [%thread] - %logger{15}%n\'
  level:
    root: info # 全局用info
    org:
      springblade: debug
