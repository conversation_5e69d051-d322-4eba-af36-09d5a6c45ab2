<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh-parent</artifactId>
        <version>3.3.0-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <artifactId>jrzh_manage</artifactId>
    <version>3.3.0-SNAPSHOT</version>
    <properties>
        <jrzh.project.id>jrzh_supply_chain</jrzh.project.id>
        <jrzh.project.version>3.3.0-SNAPSHOT</jrzh.project.version>
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <java.version>1.8</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_system_biz</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_other_api_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_front_biz</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_expense_deposit</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_expense_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!---->
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_order_financing_starter_server</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <!-- 应收账款 -->
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_receivable_starter_server</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

<!--        &lt;!&ndash; 云信&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.springblade</groupId>-->
<!--            <artifactId>jrzh_cloud_starter_server</artifactId>-->
<!--            <version>3.3.0-SNAPSHOT</version>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; 动产&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.springblade</groupId>-->
<!--            <artifactId>jrzh_asset_pledge_starter_server</artifactId>-->
<!--            <version>3.3.0-SNAPSHOT</version>-->
<!--        </dependency>-->

        <!-- 代采-->
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_proxy_starter_server</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_job</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <!--报表统计-->
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_customer_statistics</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
        </dependency>

        <!-- word文件占位解析-->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.7.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <!--word文件转pdf -->
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_financing_limit_biz</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_finance_plan_core</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_finance_plan_apply</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>jrzh_finance_plan_trading_order</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <artifactId>jrzh_external_biz</artifactId>
            <groupId>org.springblade</groupId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>
    </dependencies>


    <build>
        <finalName>${jrzh.project.id}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>cer</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                        <nonFilteredFileExtension>crt</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>snapshot</id>
            <name>snapshot maven</name>
            <url>https://ceshi.jingruiit.com:18481/nexus/content/repositories/snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>release</id>
            <name>release maven</name>
            <url>https://ceshi.jingruiit.com:18481/nexus/content/groups/public/</url>
        </repository>
    </repositories>
</project>
