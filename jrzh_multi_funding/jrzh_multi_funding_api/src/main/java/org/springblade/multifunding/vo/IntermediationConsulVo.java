package org.springblade.multifunding.vo;

import lombok.Data;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.multifunding.entity.CapitalSupplementMaterial;

import java.io.Serializable;
import java.util.List;

@Data
public class IntermediationConsulVo extends TenantEntity {

    private Long id;
    /**
     * 咨询人
     * 取自bladeUser表的id
     */
    private Long consultUserId;
    /**
     * 咨询人名称
     * 取自bladeUser表的name
     */
    private String consultUserName;
    /**
     * 产品id
     * 申请的产品id,即融资产品的id即多资方产品组的id
     */
    private Long goodsId;
    /**
     * 产品名称
     */
    private String goodsName;
    /**
     * 产品类型
     */
    private Integer goodsType;
    /**
     * 产品资料id
     * jrzh_customer_material表的id
     */
    private Long customerMaterialId;
    /**
     * 产品资料
     */
    private CustomerMaterial customerMaterial;
    /**
     * 资方补充资料
     */
    private List<CapitalSupplementMaterial> capitalSupplementMaterials;
    /**
     * 手机号
     * 哪个资方申请联系通过了, 哪个资方才可以查看到
     */
    private String phone;
    /**
     * logo
     */
    private String logo;
    /**
     * 是否展示水印
     * 0-否 1-是
     */
    private Integer isShowWatermark;
}
