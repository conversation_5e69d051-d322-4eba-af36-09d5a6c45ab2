package org.springblade.multifunding.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 居间咨询记录表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("jrzh_intermediation_consult")
public class IntermediationConsult extends TenantEntity {

    /**
     * 咨询人
     * 取自bladeUser表的id
     */
    private Long consultUserId;
    /**
     * 咨询人名称
     * 取自bladeUser表的name
     */
    private String consultUserName;
    /**
     * 产品id
     * 申请的产品id,即融资产品的id即多资方产品组的id
     */
    private Long goodsId;
    /**
     * 产品类型
     */
    private Integer goodsType;
    /**
     * 产品资料id
     * jrzh_customer_material表的id
     */
    private Long customerMaterialId;
    /**
     * 联系方式
     */
    private String phone;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * logo
     */
    private String logo;
    /**
     * 是否展示水印
     * 0-否 1-是
     */
    private Integer isShowWatermark;

}
