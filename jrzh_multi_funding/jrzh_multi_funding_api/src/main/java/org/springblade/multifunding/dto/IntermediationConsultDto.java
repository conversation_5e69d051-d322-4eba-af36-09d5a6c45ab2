package org.springblade.multifunding.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.customer.entity.CustomerMaterial;
import org.springblade.multifunding.entity.CapitalSupplementMaterial;

import java.io.Serializable;
import java.util.List;

/**
 * 发起咨询dto
 */
@Data
public class IntermediationConsultDto implements Serializable {

    private Long id;
    /**
     * 授信表单结构
     */
    @ApiModelProperty("授信表单")
    private String creditForm;
    /**
     * 客户授信表单json值
     */
    @ApiModelProperty(value = "客户补充资料json值")
    private String data;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long goodsId;
    /**
     * 补充资料
     */
    @ApiModelProperty("补充资料")
    private String supplementMaterial;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private Integer goodsType;
    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String phone;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String consultBusinessType;
    /**
     * 咨询用户名
     */
    @ApiModelProperty(value = "咨询用户名")
    private String consultUserName;
    /**
     * 租户id
     */
    private String tenantId = "000000";
    /**
     * 资方补充资料
     */
    private List<CapitalSupplementMaterial> capitalSupplementMaterials;
    /**
     * logo
     */
    private String logo;
    /**
     * 是否展示水印
     * 0-否 1-是
     */
    private Integer isShowWatermark;
    /**
     * 根据当前对象构建CustomerMaterial
     * @return
     */
    public CustomerMaterial buildCustomerMaterial() {
        CustomerMaterial customerMaterial = new CustomerMaterial();
        customerMaterial.setCreditForm(this.creditForm);
        customerMaterial.setData(this.data);
        customerMaterial.setGoodsId(this.goodsId);
        customerMaterial.setSupplementMaterial(this.supplementMaterial);
        return customerMaterial;
    }

}
