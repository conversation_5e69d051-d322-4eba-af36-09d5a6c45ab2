package org.springblade.multifunding.controller.front;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.*;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.dto.GoodsSearchDTO;
import org.springblade.customer.util.MyAuthUtil;
import org.springblade.multifunding.dto.ProductTemplateDTO;
import org.springblade.multifunding.dto.SubmitMultiFundingProductDTO;
import org.springblade.multifunding.entity.MultiFundingProduct;
import org.springblade.multifunding.service.IMultiFundingProductProcessService;
import org.springblade.multifunding.service.IMultiFundingProductService;
import org.springblade.multifunding.vo.MultiFundingProductVO;
import org.springblade.multifunding.vo.MultiGoodsContractTemplateVO;
import org.springblade.multifunding.vo.MyMultiFundingProductVO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.vo.CreditFromTemplateVO;
import org.springblade.product.moudle.creditfrom.service.ICreditFromTemplateService;
import org.springblade.system.utils.UserUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/1/18 16:26
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.CUSTOMER_WEB_BACK + CommonConstant.WEB_FRONT + "/multi-funding/product")
@Api(value = "多资方-产品", tags = "多资方-产品接口")
public class MultiFundingProductFrontController extends BladeController {

    private final IMultiFundingProductService multiFundingProductService;

    private final IMultiFundingProductProcessService multiFundingProductProcessService;

    private final ICreditFromTemplateService creditFromTemplateService;

    /**
     * 融资端 获取多资方产品列表 分页
     * @param goodsSearchDTO
     * @param query
     * @return
     */
    @PostMapping("/get-multi-funding-product")
    @ApiOperation("查询多资方产品列表")
    public R<IPage<MultiFundingProductVO>> getMultiFundingProductList(@RequestBody GoodsSearchDTO goodsSearchDTO, @RequestBody Query query) {
        // 获取租户id
        String tenantId = goodsSearchDTO.getTenantId();
        // 如果是未登录状态，则使用租户id查询
        if (StringUtil.isNotBlank(tenantId) && StringUtil.isBlank(AuthUtil.getTenantId())) {
            // 未登录 以租户id查询
            IPage<MultiFundingProductVO> data = TenantBroker.applyAs(tenantId, e -> {
                return multiFundingProductService.selectMultiFundingProductList(query, goodsSearchDTO);
            });
            return R.data(data);
        }
        return R.data(multiFundingProductService.selectMultiFundingProductList(query, goodsSearchDTO));
    }

    /**
     * 融资端 获取多资方产品详情
     * @param id 多资方产品id
     * @param tenantId 租户id
     * @return MultiFundingProductVO
     */
    @GetMapping("/get-multi-funding-product-by-id")
    @ApiOperation("根据多资方产品id-查询多资方产品详情")
    public R<MultiFundingProductVO> getMultiFundingProductById(@Valid @RequestParam Long id, @Valid @RequestParam String tenantId) {
        // 如果是未登录状态，则使用租户id查询
        if (StringUtil.isNotBlank(tenantId) && StringUtil.isBlank(AuthUtil.getTenantId())) {
            MultiFundingProductVO data = TenantBroker.applyAs(tenantId, e -> {
                return multiFundingProductService.getMultiFundingProductById(id, MyAuthUtil.getUserId());
            });
            return R.data(data);
        }
        return R.data(multiFundingProductService.getMultiFundingProductById(id, MyAuthUtil.getUserId()));
    }

    @GetMapping("/getCreditFormTemplateListBase")
    @ApiOperation("获取授信表单-基础")
    public R<List<CreditFromTemplateVO>> getCreditFromTemplateListBase(@RequestParam Long goodsId,
                                                                       @RequestParam Integer goodsType,
                                                                       @RequestParam Integer enterpriseType) {
        MultiFundingProduct multiFundingProduct = multiFundingProductService.getById(goodsId);
        Product detail = BeanUtil.copyProperties(multiFundingProduct, Product.class);
        if (ObjectUtil.isEmpty(detail)) {
            return R.fail("未查询到融资产品授信表单数据");
        }
        Long creditFormId = detail.getCreditFormId();
        if (GoodsTypeEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE.getCode().equals(goodsType) && EnterpriseTypeEnum.CORE_ENTERPRISE.getCode() == enterpriseType) {
            creditFormId = detail.getCoreCreditFormId();
        }
        return R.data(creditFromTemplateService.listByFormId(creditFormId));
    }

    @PostMapping("/get-available-product-template")
    @ApiOperation("获取合同模板")
    public R<List<MultiGoodsContractTemplateVO>> getAvailableProductTemplate(@RequestBody ProductTemplateDTO productTemplateDTO) {
        return R.data(multiFundingProductService.getAvailableProductTemplate(productTemplateDTO));
    }

    /**
     * 多资方产品 提交额度申请流程
     */
    @PostMapping("/submit-multi-funding-product-apply")
    @ApiOperation("提交额度申请流程")
    public R<Boolean> submitMultiFundingProductApply(@RequestBody SubmitMultiFundingProductDTO submitMultiFundingProductDTO) {
        submitMultiFundingProductDTO.setUserId(MyAuthUtil.getUserId());
        return R.data(multiFundingProductProcessService.submitMultiFundingProductApply(submitMultiFundingProductDTO));
    }

    /**
     * 融资产品 查看详情
     */
    @GetMapping("/get-my-multi-funding-product-detail")
    @ApiOperation("融资产品-查看详情")
    public R<List<MyMultiFundingProductVO>> getMyMultiFundingProductDetail(@RequestParam Long groupId) {
        return R.data(multiFundingProductService.getMyMultiFundingProductDetail(groupId));
    }

    /**
     * 我的产品-客户产品
     */
    @GetMapping("/get-my-multi-funding-product")
    @ApiOperation("我的产品-客户产品")
    public R<List<MyMultiFundingProductVO>> getMyMultiFundingProduct(@RequestParam String status, @RequestParam Integer enterpriseType) {
        return R.data(multiFundingProductService.getMyMultiFundingProduct(status, enterpriseType, MyAuthUtil.getUserId()));
    }

    /**
     * 融资端 获取融资咨询产品列表 分页
     * @param goodsSearchDTO
     * @param query
     * @return
     */
    @PostMapping("/getIntermediationProduct")
    @ApiOperation("查询多资方产品列表")
    public R<IPage<MultiFundingProductVO>> getIntermediationProduct(@RequestBody GoodsSearchDTO goodsSearchDTO, @RequestBody Query query) {
        // 获取租户id
        String tenantId = goodsSearchDTO.getTenantId();
        // 如果是未登录状态，则使用租户id查询
        if (StringUtil.isNotBlank(tenantId) && StringUtil.isBlank(AuthUtil.getTenantId())) {
            // 未登录 以租户id查询
            IPage<MultiFundingProductVO> data = TenantBroker.applyAs(tenantId, e -> {
                return multiFundingProductService.getIntermediationProduct(query, goodsSearchDTO);
            });
            return R.data(data);
        }
        return R.data(multiFundingProductService.getIntermediationProduct(query, goodsSearchDTO));
    }

    /**
     * 直接开通融资产品
     */
    @PostMapping("/openDirectly")
    @ApiOperation("直接开通融资产品")
    public R<Boolean> openDirectly(@RequestParam Long groupId) {
        Long userId=AuthUtil.getUserId();
        Integer enterpriseType=UserUtils.getEnterpriseType();
        return R.data(multiFundingProductService.openDirectly(groupId,userId,enterpriseType));
    }
}
