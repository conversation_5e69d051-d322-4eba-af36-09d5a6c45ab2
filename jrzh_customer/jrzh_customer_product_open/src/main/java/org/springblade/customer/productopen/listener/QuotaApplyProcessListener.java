package org.springblade.customer.productopen.listener;

import lombok.RequiredArgsConstructor;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springblade.common.constant.ProcessConstant;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.productopen.handler.BusinessProcessFactory;
import org.springblade.customer.productopen.handler.QuotaApplyHandler;
import org.springblade.product.moudle.pubproduct.service.impl.ProductFactory;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 融资企业申请额度流程监听
 *
 * <AUTHOR>
 */
@Component("quotaApplyProcessListener")
@RequiredArgsConstructor
public class QuotaApplyProcessListener implements ExecutionListener {

    private final BusinessProcessFactory businessProcessDirector;
    private final ProductFactory productFactory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notify(DelegateExecution delegateExecution) {
        Map<String, Object> variables = delegateExecution.getVariables();
        String processTerminal = (String) variables.getOrDefault(WfProcessConstant.TASK_VARIABLE_PROCESS_TERMINATE, "");
        Integer enterpriseType = (Integer) variables.get(ProcessConstant.ENTERPRISE_TYPE);
        Integer processType = (Integer) variables.get(ProcessConstant.PROCESS_TYPE);
        QuotaApplyHandler businessProcessHandler = businessProcessDirector.instanceOf(processType, enterpriseType, QuotaApplyHandler.class);
        Integer productType = (Integer) variables.get(ProcessConstant.PRODUCT_TYPE);

        if (StringUtil.isNotBlank(processTerminal) && StringUtil.equals(processTerminal, Boolean.TRUE.toString())) {
            //执行额度申请终止操作
            businessProcessHandler.close(delegateExecution);
        } else {
            //执行额度申请通过操作
            productFactory.instance(productType).checkQuotaApplyParam(variables);
            businessProcessHandler.pass(delegateExecution);
        }
    }
}
