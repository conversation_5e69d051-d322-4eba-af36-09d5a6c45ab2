/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.customer.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 个人账户实体类
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Data
@TableName("jrzh_customer_bank_card_person")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerBankCardPerson对象", description = "个人账户")
public class CustomerBankCardPerson extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 企业名称
	*/
	@ApiModelProperty(value = "企业名称")
	private String enterpriseName;
	/**
	* 企业id
	*/
	@ApiModelProperty(value = "企业id")
	private Long userId;
	/**
	* 银行Id
	*/
	@ApiModelProperty(value = "银行Id")
	private Long bankId;
	/**
	* 银行账号
	*/
	@ApiModelProperty(value = "银行账号")
	private String bankCardNo;
	/**
	* 开户银行
	*/
	@ApiModelProperty(value = "开户银行")
	private String bankDeposit;
	/**
	* 产品id
	*/
	@ApiModelProperty(value = "产品id")
	private Long goodsId;
	/**
	* 产品类型
	*/
	@ApiModelProperty(value = "产品类型")
	private Integer goodsType;
	/**
	 * 添加账户id
	 */
	@ApiModelProperty(value = "添加账户id")
	private Long customerBankId;
	/**
	 * 是否行方账户
	 * CommonConstant.YES/NO
	 */
	private Integer isBankAccount;
	/**
	 * 是否锁定
	 */
	private Integer isLock;
}
