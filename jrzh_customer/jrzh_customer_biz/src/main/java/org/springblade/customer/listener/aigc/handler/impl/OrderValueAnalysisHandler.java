package org.springblade.customer.listener.aigc.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.aigc.connector.AigcConnector;
import org.springblade.aigc.dto.WorkflowRunningDto;
import org.springblade.aigc.handler.impl.AbstractAiWorkflowRunningHandler;
import org.springblade.aigc.vo.AiWorkflowResult;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.customer.listener.aigc.entity.AigcExtractionsFinancialReport;
import org.springblade.customer.listener.aigc.enums.AiExtractionBusinessEnum;
import org.springblade.customer.listener.aigc.service.IAigcExtractionsFinancialReportService;
import org.springblade.resource.service.IParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@AllArgsConstructor
@Component
public class OrderValueAnalysisHandler extends AbstractAiWorkflowRunningHandler {
    @Autowired
    private AigcConnector aigcConnector;
    @Autowired
    private IParamService paramService;
    @Autowired
    private IAigcExtractionsFinancialReportService aigcExtractionsFinancialReportService;

    //订单融资 订单融资额度申请价值分析
    @Override
    public Integer support() {
        return AiExtractionBusinessEnum.ORDERS_VALUE_ANALYSIS.getCode();
    }

    @Override
    public void success(WorkflowRunningDto workflowRunning, AiWorkflowResult aiWorkflowResult) {
        try {
            Map<String, Object> variables = workflowRunning.getVariables();
            String extractResults = aiWorkflowResult.getResult();
            if (!ObjectUtil.isNotEmpty(variables) || !ObjectUtil.isNotEmpty(variables.get("processInstanceId"))) {
                log.error("处理AI财报提取结果时发生异常流程实例ID为空");
                return;
            }

            if(ObjectUtil.isNotEmpty(extractResults)){
                JSONObject entries = JSONUtil.parseObj(extractResults);
                String metrics = entries.get("metrics").toString();
                metrics = metrics.substring(metrics.indexOf("{"), metrics.lastIndexOf("}") + 1);
                JSONObject obj = JSONUtil.parseObj(metrics);
                obj.put("pdfUrl",entries.get("pdfUrl").toString());

                final AigcExtractionsFinancialReport report = AigcExtractionsFinancialReport
                        .builder()
                        .build();
                report.setExtractResults(String.valueOf(obj));
                report.setProcessInstanceId(variables.get("processInstanceId").toString());
                report.setCreateUser((Long) variables.get("userId"));
                report.setCreateDept((Long) variables.get("deptId"));
                report.setCreateTime(DateUtil.now());
                report.setBusinessType(AiExtractionBusinessEnum.ORDERS_VALUE_ANALYSIS.getCode());
                final String tenantId = variables.get("tenantId").toString();
                report.setTenantId(tenantId);
                TenantBroker.runAs(tenantId, e -> {
                    aigcExtractionsFinancialReportService.saveExtractResults(report);
                });
            }
        } catch (Exception e) {
            log.error("处理AI财报提取结果时发生异常", e);
        }
    }

    @Override
    public AiWorkflowResult workflowRunning(WorkflowRunningDto workflowRunning) {
        if (ObjectUtil.isEmpty(workflowRunning)) {
            throw new ServiceException("AI智能平台请求参数为空,请检查后再重试！");
        } else {
            String value = paramService.getValue("VALUE_ANALYSIS");
            if (ObjectUtil.isEmpty(value)) {
                throw new ServiceException("参数未配置！");
            } else {
                JSONObject entries = JSONUtil.parseObj(value);
                workflowRunning.setAppId((String) entries.get("AppId"));
                String bodyStr = JSONUtil.toJsonStr(workflowRunning);
                AiWorkflowResult result = this.aigcConnector.sendPost("/v1/workflow/running", bodyStr, (String) entries.get("AccessKey"));
                if (ObjectUtil.isNull(result)) {
                    throw new ServiceException("AI模型调用失败");
                }
                return result;
            }
        }
    }
}
