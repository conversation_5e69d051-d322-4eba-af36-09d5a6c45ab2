package org.springblade.expense.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.BillPayStatusEnum;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.bill.BillPayNameEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.deposit.entity.ExpenseDeposit;
import org.springblade.deposit.service.IExpenseDepositService;
import org.springblade.expense.constant.ExpenseInfoModeEnum;
import org.springblade.expense.constant.ExpenseInfoTypeEnum;
import org.springblade.expense.dto.ExpenseCreateDTO;
import org.springblade.expense.dto.ExpenseCreateRespDTO;
import org.springblade.expense.entity.ExpenseOrder;
import org.springblade.expense.entity.ExpenseOrderDetail;
import org.springblade.expense.handler.IExpenseInfoService;
import org.springblade.expense.service.IExpenseInfoBizService;
import org.springblade.expense.service.IProductExpenseService;
import org.springblade.expense.vo.ExpenseInfoExpenseVO;
import org.springblade.product.common.constant.AccountTypeEnum;
import org.springblade.product.common.entity.BillBankCarda;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.expense.constant.PayModeEnum;
import org.springblade.product.expense_relation.service.IBillBankCardaRelationService;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springblade.resource.vo.AttachInfoDTO;
import org.springblade.system.entity.User;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-12-12  09:54
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ExpenseInfoBizServiceImpl implements IExpenseInfoBizService {
    private final IProductExpenseService productExpenseService;
    private final IBillBankCardaRelationService billBankCardaRelationService;
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final IExpenseDepositService expenseDepositService;
    private final IAttachService attachService;
    private final List<IExpenseInfoService> expenseInfoServiceList;
    /**
     * 期数类型的费用计算节点 仅在列表里的节点需要进行特殊处理
     */
    private final static List<Integer> TERM_FEE_NODE = Arrays.asList(ExpenseConstant.FeeNodeEnum.ADVANCE_REPAYMENT.getCode(),
            ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode(), ExpenseConstant.FeeNodeEnum.OVERDUE_REPAYMENT.getCode()
            , ExpenseConstant.FeeNodeEnum.REDEMPTION_APPLY.getCode());

    @Override
    public List<ExpenseInfoExpenseVO> buildExpenseInfoExpenseVO(List<ExpenseOrderDetail> expenseOrderDetails) {
        if (CollUtil.isEmpty(expenseOrderDetails)) {
            return Collections.emptyList();
        }
        //收款账户
        //设置字典值
        settingDicBizValue(expenseOrderDetails);
        Map<Boolean, List<ExpenseOrderDetail>> expenseOrderMap = expenseOrderDetails.stream().collect(Collectors.partitioningBy(e -> GoodsEnum.UNIFIED.getCode().equals(e.getUnified())));
        //处理统一清分
        List<ExpenseInfoExpenseVO> expenseInfoExpenseOne = dealUnifiedExpenseOrderDetailsList(expenseOrderMap.get(true));
        //处理各自收取
        List<ExpenseInfoExpenseVO> expenseInfoExpenseTwo = dealExpenseOrderDetailsList(expenseOrderMap.get(false));
        //合并
        expenseInfoExpenseOne.addAll(expenseInfoExpenseTwo);
        return expenseInfoExpenseOne;
    }

    private void settingDicBizValue(List<ExpenseOrderDetail> expenseOrderDetails) {
        for (ExpenseOrderDetail expenseOrderDetail : expenseOrderDetails) {
            expenseOrderDetail.setFeeNodeStr(expenseOrderDetail.getFeeNodeStr());
            expenseOrderDetail.setCollectFeesNodeStr(expenseOrderDetail.getCollectFeesNodeStr());
            expenseOrderDetail.setExpenseTypeStr(expenseOrderDetail.getExpenseTypeStr());
        }
    }

    @Override
    public List<ExpenseInfoExpenseVO> buildExpenseInfoExpenseVO(List<ExpenseOrderDetail> expenseOrderDetails, Long userId, Long goodsId, Integer enterpriseType) {
        EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getByGoodsIdAndEnterpriseTypeAndEnterpriseIdBase(goodsId, enterpriseType, userId);
        User user = UserUtils.getUserById(userId);
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS = buildExpenseInfoExpenseVO(expenseOrderDetails);
        //付款账号填充
        for (ExpenseInfoExpenseVO expenseInfoExpenseVO : expenseInfoExpenseVOS) {
            expenseInfoExpenseVO.setPayAccountNo(enterpriseQuota.getBankCardNo());
            expenseInfoExpenseVO.setPayAccountName(user.getName());
            expenseInfoExpenseVO.setExpenseTypeName(user.getName());
            expenseInfoExpenseVO.setPayOpenBank(enterpriseQuota.getBank());
            expenseInfoExpenseVO.setBankCode(enterpriseQuota.getBankUnionCode());
        }
        return expenseInfoExpenseVOS;
    }

    @Override
    public List<ExpenseInfoExpenseVO> listExpenseInfoList(String financeNo, Integer type, Integer feeNode, Long bankCardId) {
        //费用
        List<Integer> status = Arrays.asList(BillPayStatusEnum.BILL_ALREADY.getCode(), BillPayStatusEnum.BILL_PENDING.getCode());
        List<ExpenseOrderDetail> expenseOrderDetails = productExpenseService.listExpenseOrderDetail(financeNo, type, feeNode, CommonConstant.YES, status);
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS = buildExpenseInfoExpenseVO(expenseOrderDetails);
        //当前节点是还款节点
        if (TERM_FEE_NODE.contains(feeNode)) {
            //加入资方独立费用
            List<ExpenseInfoExpenseVO> unHasDetailExpenseOrder = listUnHasDetailOrderDetail(financeNo);
            expenseInfoExpenseVOS = Stream.concat(expenseInfoExpenseVOS.stream(), unHasDetailExpenseOrder.stream()).collect(Collectors.toList());
            expenseInfoExpenseVOS.forEach(e -> {
                e.setExpenseInfoType(ExpenseInfoTypeEnum.REPAYMENT.getCode());
            });
        }
        //保证金
        List<ExpenseInfoExpenseVO> cashDepositList = getExpenseInfoExpenseVOS(financeNo);
        //合并
        List<ExpenseInfoExpenseVO> allExpenseInfo = Stream.concat(cashDepositList.stream(), expenseInfoExpenseVOS.stream()).collect(Collectors.toList());
        //附件设置
        settingExpenseInfoAttach(allExpenseInfo);
        return allExpenseInfo;
    }

    private void settingExpenseInfoAttach(List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS) {
        List<Long> attachIds = expenseInfoExpenseVOS.stream().filter(e -> StringUtil.isNotBlank(e.getPayAttachId())).flatMap(e -> Func.toLongList(e.getPayAttachId()).stream()).collect(Collectors.toList());
        Map<Long, Attach> attachMap = CollUtil.isNotEmpty(attachIds) ? attachService.listByIds(attachIds).stream().collect(Collectors.toMap(BaseEntity::getId, e -> e)) : MapUtil.newHashMap();
        expenseInfoExpenseVOS.forEach(e -> {
            if (StringUtil.isNotBlank(e.getPayAttachId())) {
                List<Attach> attachList = new LinkedList<>();
                List<AttachInfoDTO> attachInfoDTOList = Func.toLongList(e.getPayAttachId()).stream().map(id -> {
                    Attach attach = attachMap.get(id);
                    if (ObjectUtil.isEmpty(attach)) {
                        return null;
                    }
                    AttachInfoDTO attachInfoDTO = new AttachInfoDTO();
                    attachInfoDTO.setAttachId(attach.getId());
                    attachInfoDTO.setName(attach.getOriginalName());
                    attachInfoDTO.setUrl(attach.getLink());
                    attachList.add(attach);
                    return attachInfoDTO;
                }).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
                e.setAttachList(attachInfoDTOList);
                e.setOriginAttachList(attachList);
            }
        });
    }

    private List<ExpenseInfoExpenseVO> listUnHasDetailOrderDetail(String financeNo) {
        List<ExpenseOrder> expenseOrders = productExpenseService.listUnHasDetailExpenseOrder(financeNo);
        if (CollUtil.isEmpty(expenseOrders)) {
            return Collections.emptyList();
        }
        ExpenseOrder first = CollUtil.getFirst(expenseOrders);
        BillBankCarda billBankCarda = billBankCardaRelationService.selectCardaRelationByGoodsId(first.getGoodsId(), AccountTypeEnum.CAPITAL_ACCOUNT.getCode());

        if (billBankCarda == null) {
            throw new ServiceException("资方费用配置有误,请联系管理员调整!");
        }

        return expenseOrders.stream().map(expenseOrder -> {
            ExpenseInfoExpenseVO expenseInfoExpenseVO = BeanUtil.copyProperties(expenseOrder, ExpenseInfoExpenseVO.class);
            //填充基础信息
            expenseInfoExpenseVO.setExpenseInfoType(ExpenseInfoTypeEnum.EXPENSE.getCode());
            expenseInfoExpenseVO.setAccountName(billBankCarda.getEnterpriseName());
            expenseInfoExpenseVO.setEnterpriseName(billBankCarda.getEnterpriseName());
            expenseInfoExpenseVO.setOpenBank(billBankCarda.getBankDeposit());
            expenseInfoExpenseVO.setAccountNo(billBankCarda.getBankCardNo());
            expenseInfoExpenseVO.setExpenseTypeName("资方费用");
            expenseInfoExpenseVO.setExpenseOrderDetail(Collections.emptyList());
            expenseInfoExpenseVO.setTotalAmount(expenseOrder.getExpenseOrderFee().add(expenseOrder.getExAmount()));
            expenseInfoExpenseVO.setPayAmount(expenseOrder.getAmount());
            expenseInfoExpenseVO.setExAmount(expenseOrder.getExAmount());
            expenseInfoExpenseVO.setExpenseChargeMethod(2);
            return expenseInfoExpenseVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ExpenseInfoExpenseVO> getExpenseInfoExpenseVOS(String financeNo) {
        ExpenseDeposit cashDeposit = expenseDepositService.getCashDeposit(financeNo);
        List<ExpenseInfoExpenseVO> cashDepositList = buildExpenseDepositInfoExpenseVO(cashDeposit);
        return cashDepositList;
    }

    private List<ExpenseInfoExpenseVO> buildExpenseDepositInfoExpenseVO(ExpenseDeposit cashDeposit) {
        if (ObjectUtil.isEmpty(cashDeposit)) {
            return Collections.emptyList();
        }
        BillBankCarda billBankCarda = billBankCardaRelationService.selectCardaRelationByGoodsId(cashDeposit.getGoodsId(), AccountTypeEnum.CASH_DEPOSIT_TAKE_ACCOUNT.getCode());
        if (ObjectUtil.isEmpty(billBankCarda)) {
            throw new ServiceException("保证金账户未配置");
        }
        ExpenseInfoExpenseVO expenseInfoExpenseVO = new ExpenseInfoExpenseVO();
        expenseInfoExpenseVO.setExpenseInfoType(ExpenseInfoTypeEnum.DEPOSIT.getCode());
        expenseInfoExpenseVO.setAccountName(billBankCarda.getEnterpriseName());
        expenseInfoExpenseVO.setOpenBank(billBankCarda.getBankDeposit());
        expenseInfoExpenseVO.setAccountNo(billBankCarda.getBankCardNo());
        expenseInfoExpenseVO.setExpenseTypeName("保证金缴纳");
        expenseInfoExpenseVO.setPayMode(PayModeEnum.PAY_MODE_BELOW.getCode());
        expenseInfoExpenseVO.setExpenseDeposit(cashDeposit);
        expenseInfoExpenseVO.setTotalAmount(cashDeposit.getPayableAmount());
        expenseInfoExpenseVO.setExpenseChargeMethod(2);
        expenseInfoExpenseVO.setEnterpriseName(billBankCarda.getEnterpriseName());
        expenseInfoExpenseVO.setBillExpenseNo(cashDeposit.getCashDepositNo());
        expenseInfoExpenseVO.setPayAttachId(cashDeposit.getPayedVoucher());
        expenseInfoExpenseVO.setPaymentMethod(BillPayNameEnum.OFFLINE_PAY.getStatus());
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS = new ArrayList<>();
        expenseInfoExpenseVOS.add(expenseInfoExpenseVO);
        return expenseInfoExpenseVOS;
    }

    private List<ExpenseCreateRespDTO> genExpenseOrderListByExpenseInfoExpenseVO(List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS, ExpenseCreateDTO expenseCreateDTO, Integer feeNode) {
        final Integer OFF_LINE_PAY = 1;
        final Integer OFF_LINE_BANK_PAY = 4;
        //生成费用单
        List<ExpenseCreateRespDTO> list = new ArrayList<>();
        for (ExpenseInfoExpenseVO expenseInfoExpenseVO : expenseInfoExpenseVOS) {
            Integer payMethod = OFF_LINE_PAY.equals(expenseInfoExpenseVO.getPayMode()) || OFF_LINE_BANK_PAY.equals(expenseInfoExpenseVO.getPayMode()) ?
                    BillPayNameEnum.OFFLINE_PAY.getStatus() : null;
            expenseCreateDTO.setBillExpenseNo(CodeUtil.generateCode(CodeEnum.BILL_EXPENSE_ORDER_NO));
            expenseCreateDTO.setPaymentMethod(payMethod);
            ExpenseCreateRespDTO expenseOrder = productExpenseService.expenseOrderCreate(expenseInfoExpenseVO.getExpenseOrderDetail(), expenseCreateDTO);
            list.add(expenseOrder);
        }
        return list;
    }

    @Override
    public List<ExpenseCreateRespDTO> genExpenseOrderListByExpenseInfoExpenseVO(List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS, String financeNo, Integer feeNode, Long userId, Integer userType) {
        ExpenseCreateDTO build = ExpenseCreateDTO.builder()
                .bizNo(financeNo)
                .userType(userType)
                .userId(userId).build();
        return genExpenseOrderListByExpenseInfoExpenseVO(expenseInfoExpenseVOS, build, feeNode);
    }

    @Override
    public List<ExpenseCreateRespDTO> genExpenseOrderList(List<ExpenseOrderDetail> expenseOrderDetailList, String financeNo, Integer feeNode, Long userId, Integer userType) {
        //排除掉非这个节点需要收费的费用详情
        List<ExpenseOrderDetail> needCollectExpenseOrderDetailList = expenseOrderDetailList.stream()
                .filter(e -> financeNo.equals(e.getFinanceNo())
                        && feeNode.equals(e.getCollectFeesNode()))
                .collect(Collectors.toList());
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS = buildExpenseInfoExpenseVO(needCollectExpenseOrderDetailList);
        return genExpenseOrderListByExpenseInfoExpenseVO(expenseInfoExpenseVOS, financeNo, feeNode, userId, userType);
    }

    @Override
    public List<ExpenseCreateRespDTO> genExpenseOrderList(List<ExpenseOrderDetail> expenseOrderDetailList, ExpenseCreateDTO expenseCreateDTO, Integer feeNode) {
        //排除掉非这个节点需要收费的费用详情
        List<ExpenseOrderDetail> needCollectExpenseOrderDetailList = expenseOrderDetailList.stream()
                .filter(e -> expenseCreateDTO.getBizNo().equals(e.getFinanceNo())
                        && feeNode.equals(e.getCollectFeesNode()))
                .collect(Collectors.toList());
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOS = buildExpenseInfoExpenseVO(needCollectExpenseOrderDetailList);
        return genExpenseOrderListByExpenseInfoExpenseVO(expenseInfoExpenseVOS, expenseCreateDTO, feeNode);
    }

    /**
     * 处理各自收取费用
     *
     * @param expenseOrderDetailsList
     * @return
     */
    private List<ExpenseInfoExpenseVO> dealExpenseOrderDetailsList(List<ExpenseOrderDetail> expenseOrderDetailsList) {
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOList = new ArrayList<>();
        if (CollUtil.isEmpty(expenseOrderDetailsList)) {
            return expenseInfoExpenseVOList;
        }
        //查询所有已存在的费用订单
        List<String> existOrderBillNo = expenseOrderDetailsList.stream().map(ExpenseOrderDetail::getBillExpenseNo)
                .filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, ExpenseOrder> expenseOrderMap = CollUtil.isEmpty(existOrderBillNo) ? MapUtil.newHashMap() :
                productExpenseService.listExpenseOrderByBillExpenseNos(existOrderBillNo).stream().collect(Collectors.toMap(ExpenseOrder::getBillExpenseNo, e -> e));
        //区分线上线下
        Map<Boolean, List<ExpenseOrderDetail>> expenseDetailListMap = expenseOrderDetailsList.stream()
                .collect(Collectors.partitioningBy(e -> PayModeEnum.PAY_MODE_ONLINE.getCode().equals(e.getCostPayMode())));
        List<ExpenseOrderDetail> onlineExpenseOrderDetails = expenseDetailListMap.get(true);
        List<ExpenseOrderDetail> offlineExpenseOrderDetails = expenseDetailListMap.get(false);
        //线上统一进行创建缴纳费用信息 支付后按收款账户进行分账处理
        List<ExpenseInfoExpenseVO> one = dealOnLineExpenseOrderDetailsList(onlineExpenseOrderDetails, expenseOrderMap);
        //线下按费用类型进行分组处理
        List<ExpenseInfoExpenseVO> two = dealOffLineExpenseOrderDetailList(offlineExpenseOrderDetails, expenseOrderMap);
        //合并返回
        List<ExpenseInfoExpenseVO> all = new ArrayList<>();
        all.addAll(one);
        all.addAll(two);
        return all;
    }

    private List<ExpenseInfoExpenseVO> dealOffLineExpenseOrderDetailList(List<ExpenseOrderDetail> offlineExpenseOrderDetails
            , Map<String, ExpenseOrder> expenseOrderMap) {
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOList = new ArrayList<>();
        if (CollUtil.isEmpty(offlineExpenseOrderDetails)) {
            return expenseInfoExpenseVOList;
        }
        //按父类费用名称进行分组
        Map<String, List<ExpenseOrderDetail>> accountExpenseDetailMap = offlineExpenseOrderDetails.stream()
                .collect(Collectors.groupingBy(ExpenseOrderDetail::getParenExpenseName));
        for (String parenExpenseName : accountExpenseDetailMap.keySet()) {
            List<ExpenseOrderDetail> expenseOrderDetailList = accountExpenseDetailMap.get(parenExpenseName);
            //校验费用详情
            validExpenseOrderDetailList(expenseOrderDetailList);
            //构建缴费信息
            ExpenseInfoExpenseVO expenseInfoExpenseVO = buildExpenseInfo(expenseOrderDetailList, CommonConstant.NO);
            //费用已生成,填充费用信息
            settingExpenseOrderInfo(expenseInfoExpenseVO, expenseOrderMap);
            expenseInfoExpenseVOList.add(expenseInfoExpenseVO);
        }
        return expenseInfoExpenseVOList;
    }


    private ExpenseInfoExpenseVO buildExpenseInfo(List<ExpenseOrderDetail> expenseOrderDetailList, Integer isBank) {
        //调用银行相关接口进行构建ExpenseInfoExpenseVO
        ExpenseOrderDetail first = CollUtil.getFirst(expenseOrderDetailList);
        Integer costPayMode = first.getCostPayMode();
        IExpenseInfoService expenseInfoService = getExpenseInfoService(getExpenseInfoModeEnum(costPayMode, isBank));
        return expenseInfoService.buildExpenseInfoExpenseVO(expenseOrderDetailList);
    }

    private List<ExpenseInfoExpenseVO> dealOnLineExpenseOrderDetailsList(List<ExpenseOrderDetail> onlineExpenseOrderDetails
            , Map<String, ExpenseOrder> expenseOrderMap) {
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOList = new ArrayList<>();
        if (CollUtil.isEmpty(onlineExpenseOrderDetails)) {
            return expenseInfoExpenseVOList;
        }
        //校验详情列表
        validExpenseOrderDetailList(onlineExpenseOrderDetails);
        //构建缴费信息
        ExpenseInfoExpenseVO expenseInfo = buildExpenseInfo(onlineExpenseOrderDetails, CommonConstant.NO);
        //设置支付状态
        settingExpenseOrderInfo(expenseInfo, expenseOrderMap);
        return Collections.singletonList(expenseInfo);
    }

    private void validExpenseOrderDetailList(List<ExpenseOrderDetail> onlineExpenseOrderDetails) {
        long count = onlineExpenseOrderDetails.stream().map(ExpenseOrderDetail::getBillExpenseNo).distinct().count();
        if (count > 1) {
            throw new ServiceException("费用详情错误,请重新生成费用详情");
        }
    }

    private void settingExpenseOrderInfo(ExpenseInfoExpenseVO expenseInfo, Map<String, ExpenseOrder> expenseOrderMap) {
        List<ExpenseOrderDetail> expenseOrderDetail = expenseInfo.getExpenseOrderDetail();
        String billExpenseNo = CollUtil.getFirst(expenseOrderDetail).getBillExpenseNo();
        if (StringUtil.isBlank(billExpenseNo)) {
            expenseInfo.setPaymentStatus(BillPayStatusEnum.BILL_PENDING.getCode());
            return;
        }
        ExpenseOrder expenseOrder = expenseOrderMap.get(billExpenseNo);
        if (ObjectUtil.isEmpty(expenseOrder)) {
            throw new ServiceException("费用订单不存在");
        }
        BeanUtil.copyProperties(expenseOrder, expenseInfo);
        expenseInfo.setTotalAmount(expenseOrder.getExpenseOrderFee().add(expenseOrder.getExAmount()));
        expenseInfo.setPayAmount(expenseOrder.getAmount());
    }

    /**
     * 处理统一清分
     *
     * @param expenseOrderDetailsList
     * @return
     */
    private List<ExpenseInfoExpenseVO> dealUnifiedExpenseOrderDetailsList(List<ExpenseOrderDetail> expenseOrderDetailsList) {
        List<ExpenseInfoExpenseVO> expenseInfoExpenseVOList = new ArrayList<>();
        if (CollUtil.isEmpty(expenseInfoExpenseVOList)) {
            return expenseInfoExpenseVOList;
        }
        //按收款账号进行分组
        Map<Integer, List<ExpenseOrderDetail>> accountExpenseDetailMap = expenseOrderDetailsList.stream()
                .collect(Collectors.groupingBy(ExpenseOrderDetail::getExpenseType));
        for (Integer expenseTYpe : accountExpenseDetailMap.keySet()) {
            //调用银行相关接口进行构建ExpenseInfoExpenseVO
            expenseInfoExpenseVOList.add(buildExpenseInfo(accountExpenseDetailMap.get(expenseTYpe), CommonConstant.YES));
        }
        return expenseInfoExpenseVOList;
    }

    private IExpenseInfoService getExpenseInfoService(Integer expenseMode) {
        for (IExpenseInfoService expenseInfoService : expenseInfoServiceList) {
            if (expenseInfoService.support().getCode().equals(expenseMode)) {
                return expenseInfoService;
            }
        }
        throw new UnsupportedOperationException("策略类未实现");
    }

    /**
     * 获取类型
     *
     * @param payMode 类型
     * @param isBank
     * @return
     */
    private Integer getExpenseInfoModeEnum(Integer payMode, Integer isBank) {
        if (CommonConstant.YES.equals(isBank)) {
            if (PayModeEnum.PAY_MODE_ONLINE.getCode().equals(payMode)) {
                return ExpenseInfoModeEnum.BANK_ON_LINE.getCode();
            } else {
                return ExpenseInfoModeEnum.BANK_OFF_LINE.getCode();
            }
        }
        if (PayModeEnum.PAY_MODE_ONLINE.getCode().equals(payMode)) {
            return ExpenseInfoModeEnum.ON_LINE.getCode();
        } else {
            return ExpenseInfoModeEnum.OFF_LINE.getCode();
        }
    }
}
