export const routerMapKeyToPath = {
  process_height_trade_background: '/work/mywork/process/trade',
  // 产品确认
  goods_confirm: '/work/mywork/process/product/productConfirmation',
  // 额度申请-融资
  apply_quota: '/work/mywork/process/product/creditlimitfinancing',
  // 额度申请-融资-AI
  apply_quota__AI: '/work/mywork/process/product/creditlimitfinancing',
  // 额度申请-订单融资
  order_financing_apply_quota: '/work/mywork/process/product/creditlimitfinancing',
  // 额度申请-订单融资-AI
  order_financing_apply_quota__AI: '/work/mywork/process/product/creditlimitfinancing',
  // // 额度申请-订单融资
  order_financing_apply_quota__hkzh: '/work/mywork/process/product/creditlimitfinancing',
  // 产品组路由申请
  processTypeProductApply: '/work/mywork/process/product/productgrouprouting',

  // 解冻申请流程-应收
  process_receive_unfreeze_goods_apply: '/work/mywork/process/product/thawApplication',
  // 解冻申请流程-订单
  process_order_financing_unfreeze_goods_apply: '/work/mywork/process/product/thawApplication',
  // 逾期协商-应收
  process_receive_overdue_consult_apply: '/work/mywork/process/product/overdueConsultationDisposable',
  // 代采额度申请-融资
  agent_apply_quota: '/work/mywork/process/product/creditlimitfinancing',
  // 动产质押额度申请-融资
  goods_pledge_open_apply: '/work/mywork/process/product/chattelmortgagep/quotaapplication',
  // 核心企业自主额度申请
  core_auto_apply_quota: '/work/mywork/process/product/creditlimitfinancing',
  // 核心企业自主额度申请AI
  core_auto_apply_quota__ai: '/work/mywork/process/product/creditlimitfinancingAI',
  // 核心企业自主额度申请-订单融资
  order_financing_core_auto_apply_quota: '/work/mywork/process/product/creditlimitfinancing',
  // 云信额度申请
  cloud_apply_quota: '/work/mywork/process/product/creditlimitfinancing',
  // 额度申请-核心
  core_apply_quota: '/work/mywork/process/product/coreenterprisecreditlimit',
  //额度申请-订单融资核心
  order_financing_core_apply_quota: '/work/mywork/process/product/coreenterprisecreditlimit',
  // 额度申请-核心
  financing_flag_apply_quota: '/work/mywork/process/product/financingFlagEnterpriseCreditLimit',
  // 额度激活
  quota_active: '/work/mywork/process/product/amountOfActivated',
  // 额度激活 -订单融资
  order_financing_quota_active: '/work/mywork/process/product/amountOfActivated',
  // 代采额度激活
  agent_quota_active: '/work/mywork/process/product/amountOfActivated',
  // 核心企业自主额度激活
  core_auto_quota_active: '/work/mywork/process/product/amountOfActivated',
  // 核心企业自主额度激活
  order_financing_core_auto_quota_active: '/work/mywork/process/product/amountOfActivated',
  // 核心企业云信额度激活
  cloud_quota_active: '/work/mywork/process/product/amountOfActivated',
  // 自动放款融资申请
  auto_loan_finance_apply: '/work/mywork/process/product/applicationsForVoluntarily',
  // 融资申请
  finance_apply: '/work/mywork/process/product/applicationsFor',
  // 订单融资-自动放款融资申请
  auto_loan_finance_apply_dingdan: '/work/mywork/process/product/dingdanrongzhi-bigb/applicationsForVoluntarily',
  // 订单融资-融资申请
  finance_apply_dingdan: '/work/mywork/process/product/dingdanrongzhi-bigb/applicationsFor',
  // 动产质押融资申请
  pledge_goods_finance: '/work/mywork/process/product/chattelmortgagep/financingapplication',
  // 动产质押放款申请（去确认）
  pledge_goods_loan: '/work/mywork/process/product/chattelmortgagep/loanmovableproperty',
  // 动产质押赎货申请
  pledge_redeem_apply: '/work/mywork/process/product/chattelmortgagep/redeemapplypledgemovables',
  // 动产质押赎货确认
  pledge_redeem_confirm: '/work/mywork/process/product/chattelmortgagep/redeemconfirmpledegmovables',
  // 货物处置申请
  pledge_cargo_solve: '/work/mywork/process/goodsApplications',

  // 代采自动放款申请
  purchase_auto_apply: '/work/mywork/process/product/purchaseFinancing',
  // 代采申请
  purchase_apply: '/work/mywork/process/product/purchaseFinancing',
  // 代采融资确认
  purchase_financing_confirm: '/work/mywork/process/product/purchaseSubmit',

  // 放款申请
  loan_apply: '/work/mywork/process/product/loanApplication',
  // 订单融资-放款申请
  loan_apply_dingdan: '/work/mywork/process/product/dingdanrongzhi-bigb/loanApplication',
  // 调息申请
  adjust_interest_apply: '/work/mywork/process/product/regulatingBreathing',
  // 减免申请
  derate_alteration_apply: '/work/mywork/process/product/debtRelief',
  // 应收账款确权
  process_lower_sales_contract: '/work/mywork/process/receivableConfirm',
  // 核心企业入驻审批
  process_core_enter_system: '/work/mywork/process/coreEnterpriseAccess',
  cloud_bill: '/work/mywork/process/product/applyCloudBillingLetter',
  // 额度申请-核心-云信
  core_apply_quota_cloud: '/work/mywork/process/product/coreenterprisecreditlimit',
  // 云信融资申请
  cloud_financing: '/work/mywork/process/product/cloudFinancingApplication',
  // 赎货申请
  redeem_apply: '/work/mywork/process/RedeemApply',
  // 赎货确认
  redeem_confirm: '/work/mywork/process/RedeemConfirm',

  // 云信放款
  cloud_load_apply: '/work/mywork/process/CloudLoanConfirm',
  // 展期申请
  loan_delay_apply: '/work/mywork/process/RenewalApply',
  // 展期确认
  loan_delay_confirm: '/work/mywork/process/RenewalConfirm',

  // 云信额度调整
  core_cloud_quota_update: '/work/mywork/process/coreEnpriseAdjustment',
  // 核心应收
  core_receive_qupta_update: '/work/mywork/process/coreEnpriseAdjustment',
  // 融资应收
  financing_order_financing_qupta_update: '/work/mywork/process/coreEnpriseAdjustment',

  //代采额度调整
  financing_purchase_quota_update: '/work/mywork/process/financingFlagEnterpriseAdjustment',
  // 融资应收
  financing_receive_qupta_update: '/work/mywork/process/financingFlagEnterpriseAdjustment',
  // advance_settled_apply 提前结清
  advance_settled_apply: '/work/mywork/process/advancerepayapply',
  order_financing_advance_settled_apply: '/work/mywork/process/advancerepayapply',
  agent_purchase_change_apply: '/work/mywork/process/purchaseRefund',

  // 多资方
  finance_apply_confirm_multi: '/work/mywork/process/product/applyConfirmMulti',
}
