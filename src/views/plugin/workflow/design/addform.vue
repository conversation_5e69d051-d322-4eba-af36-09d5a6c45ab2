<template>
  <basic-container>
    <div class="external-form">
      <!-- up -->
      <header>
        <h1 class="title-top">基本信息</h1>
        <div class="guaranteeSetTop">
          <avue-form
            ref="basicInformation"
            :option="option"
            v-model="information"
          >
          </avue-form>
        </div>
      </header>
      <!-- down -->
      <div class="module-box">
        <el-collapse v-model="activeNames">
          <transition-group tag="ul" class="drag-list" name="drag">
            <el-collapse-item
              class="fromCollapse"
              :title="item.templateName"
              :name="item.orderNum"
              v-for="(item, index) in list"
              :key="item.orderNum"
              :draggable="draggStart"
              @dragstart.native="
                draggStart && dragStartCard($event, 'first', index)
              "
              @dragenter.native="
                draggStart && dragEnterCard($event, item, index, 'first')
              "
              @dragover.native="dragOverCard($event, item, index)"
              @drop.native="dropCard($event, item, index)"
            >
              <!-- title的solt -->
              <template slot="title">
                <div class="fromHeader" @keyup.stop="prevent()">
                  <div class="from-input-left">
                    <i
                      class="el-icon-caret-bottom"
                      :class="{
                        'i-active':
                          item.orderNum ==
                          activeNames.filter(
                            filterItem => filterItem == item.orderNum
                          )[0],
                      }"
                    />
                    <div class="edit-input-box">
                      <div @click.stop="prevent" class="edit-input">
                        <el-input
                          v-if="detail && index == inpClickNum"
                          v-model="item.templateName"
                          placeholder="请输入模版名称"
                          clearable
                          size="medium"
                          :id="`index${index}`"
                          @blur="draggStopFlip('launch')"
                          @focus="draggStop()"
                        ></el-input>
                        <span
                          v-else
                          @click.stop="detailFlip(index)"
                          :class="{ inpactive: !item.templateName }"
                        >
                          {{
                            item.templateName
                              ? item.templateName
                              : '点击编辑模版名称'
                          }}
                        </span>
                      </div>
                      <i
                        class="el-icon-edit"
                        @click.stop="detailFlip(index)"
                        v-if="!lookCollect"
                      />
                    </div>
                    <div class="edit-input-box">
                      <div @click.stop="prevent" class="edit-input">
                        <el-input
                          v-if="
                            detail && `${index}templateKeys` == inpKeyClickNum
                          "
                          v-model="item.templateKey"
                          placeholder="请输入模版Key"
                          clearable
                          size="medium"
                          :id="`index${index}templateKeys`"
                          @blur="draggStopFlip('launch')"
                          @focus="draggStop()"
                        ></el-input>
                        <span
                          v-else
                          @click.stop="detailFlip(`${index}templateKeys`)"
                          :class="{ inpactive: !item.templateKey }"
                        >
                          {{
                            item.templateKey
                              ? item.templateKey
                              : '点击编辑模版Key'
                          }}
                        </span>
                      </div>
                      <i
                        class="el-icon-edit"
                        @click.stop="detailFlip(`${index}templateKeys`)"
                        v-if="!lookCollect"
                      />
                    </div>
                  </div>
                  <div class="fromHeadermenu" v-if="!lookCollect">
                    <i
                      :class="{
                        'el-icon-plus': item.grade !== '1',
                      }"
                      @click.stop="operateMenuPush(item)"
                    ></i>
                    <i
                      :class="{
                        'el-icon-top': true,
                        'el-icon-disable': index == 0,
                      }"
                      @click.stop="operateMenuUp(item, index)"
                    ></i>
                    <i
                      :class="{
                        'el-icon-bottom': true,
                        'el-icon-disable': index == list.length - 1,
                      }"
                      @click.stop="operateMenuDown(item, index)"
                    ></i>
                    <i
                      class="el-icon-delete-solid"
                      @click.stop="operateMenuDelete(item, index)"
                    ></i>
                  </div>
                </div>
              </template>
              <!-- 单级子表单 -->
              <div class="guaranteeSetBottom" v-if="item.grade === '1'">
                <avue-form :option="subformOption" v-model="item.subForm" />
              </div>
              <!-- 二级模块&三级模块collapse -->
              <el-collapse
                v-else-if="item.grade === '2' || item.grade === '3'"
                v-model="item.listedSecond.activeNames"
              >
                <transition-group tag="ul" class="drag-list" name="drag">
                  <el-collapse-item
                    class="fromCollapse from-collapse-children"
                    :title="itemed.templateNameSecond"
                    :name="itemed.orderNumSecond"
                    v-for="(itemed, indexed) in item.listedSecond"
                    :key="itemed.orderNumSecond"
                    :draggable="draggStartSecond"
                    @dragstart.native="
                      draggStartSecond &&
                        dragStartCard($event, 'second', indexed)
                    "
                    @dragenter.native="
                      draggStartSecond &&
                        dragEnterCard($event, item, indexed, 'second')
                    "
                    @dragover.native="dragOverCard($event, itemed, indexed)"
                    @drop.native="dropCard($event, itemed, indexed)"
                  >
                    <!-- title的solt -->
                    <template slot="title">
                      <div class="fromHeader" @keyup.stop="prevent()">
                        <div class="from-input-left">
                          <i
                            class="el-icon-caret-bottom"
                            :class="{
                              'i-active':
                                item.listedSecond.activeNames &&
                                itemed.orderNumSecond ==
                                  item.listedSecond.activeNames.filter(
                                    filterItem =>
                                      filterItem == itemed.orderNumSecond
                                  )[0],
                            }"
                          />
                          <div class="edit-input-box">
                            <div @click.stop="prevent" class="edit-input">
                              <el-input
                                v-if="
                                  detail &&
                                  `listedSecond${index}${indexed}` ==
                                    inpClickNum
                                "
                                v-model="itemed.templateNameSecond"
                                placeholder="请输入模版名称"
                                clearable
                                size="medium"
                                :id="`indexlistedSecond${index}${indexed}`"
                                @blur="draggStopFlip('launch')"
                                @focus="draggStop()"
                              ></el-input>
                              <span
                                v-else
                                @click.stop="
                                  detailFlip(`listedSecond${index}${indexed}`)
                                "
                                :class="{
                                  inpactive: !itemed.templateNameSecond,
                                }"
                              >
                                {{
                                  itemed.templateNameSecond
                                    ? itemed.templateNameSecond
                                    : '点击编辑模版名称'
                                }}
                              </span>
                            </div>
                            <i
                              class="el-icon-edit"
                              @click.stop="
                                detailFlip(`listedSecond${index}${indexed}`)
                              "
                              v-if="!lookCollect"
                            />
                          </div>
                          <div class="edit-input-box">
                            <div @click.stop="prevent" class="edit-input">
                              <el-input
                                v-if="
                                  detail &&
                                  `listedSecond${index}${indexed}templateKeys` ==
                                    inpKeyClickNum
                                "
                                v-model="itemed.templateKeySecond"
                                placeholder="请输入模版Key"
                                clearable
                                size="medium"
                                :id="`indexlistedSecond${index}${indexed}templateKeys`"
                                @blur="draggStopFlip('launch')"
                                @focus="draggStop()"
                              ></el-input>
                              <span
                                v-else
                                @click.stop="
                                  detailFlip(
                                    `listedSecond${index}${indexed}templateKeys`
                                  )
                                "
                                :class="{
                                  inpactive: !itemed.templateKeySecond,
                                }"
                              >
                                {{
                                  itemed.templateKeySecond
                                    ? itemed.templateKeySecond
                                    : '点击编辑模版Key'
                                }}
                              </span>
                            </div>
                            <i
                              class="el-icon-edit"
                              @click.stop="
                                detailFlip(
                                  `listedSecond${index}${indexed}templateKeys`
                                )
                              "
                              v-if="!lookCollect"
                            />
                          </div>
                        </div>
                        <div class="fromHeadermenu" v-if="!lookCollect">
                          <i
                            :class="{
                              'el-icon-plus':
                                item.grade !== '1' && item.grade !== '2',
                            }"
                            @click.stop="operateMenuPush(itemed)"
                          ></i>
                          <i
                            :class="{
                              'el-icon-top': true,
                              'el-icon-disable': indexed == 0,
                            }"
                            @click.stop="operateMenuUp(item, indexed, 'second')"
                          ></i>
                          <i
                            :class="{
                              'el-icon-bottom': true,
                              'el-icon-disable':
                                indexed == item.listedSecond.length - 1,
                            }"
                            @click.stop="
                              operateMenuDown(item, indexed, 'second')
                            "
                          ></i>
                          <i
                            class="el-icon-delete-solid"
                            @click.stop="
                              operateMenuDelete(item, indexed, 'second')
                            "
                          ></i>
                        </div>
                      </div>
                    </template>
                    <!-- 二级子表单 -->
                    <div class="guaranteeSetBottom" v-if="item.grade === '2'">
                      <avue-form
                        :option="subformOption"
                        v-model="itemed.subFormSecond"
                      />
                    </div>
                    <!-- 三级模块子折叠面板 -->
                    <el-collapse
                      v-else-if="item.grade === '3'"
                      v-model="itemed.listedThirdly.activeNames"
                    >
                      <transition-group tag="ul" class="drag-list" name="drag">
                        <el-collapse-item
                          class="fromCollapse from-collapse-childrenThirdly"
                          v-for="(
                            childrenItem, childrenIndex
                          ) in itemed.listedThirdly"
                          :title="childrenItem.templateNameThirdlyPack"
                          :name="childrenItem.orderNumThirdlyPack"
                          :key="childrenItem.orderNumThirdlyPack"
                          :draggable="draggStartThirdly"
                          @dragstart.native="
                            draggStartThirdly &&
                              dragStartCard($event, 'thirdly', childrenIndex)
                          "
                          @dragenter.native="
                            draggStartThirdly &&
                              dragEnterCard(
                                $event,
                                itemed,
                                childrenIndex,
                                'thirdly'
                              )
                          "
                          @dragover.native="
                            dragOverCard($event, childrenItem, childrenIndex)
                          "
                          @drop.native="
                            dropCard($event, childrenItem, childrenIndex)
                          "
                        >
                          <!-- title的solt -->
                          <template slot="title">
                            <div class="fromHeader" @keyup.stop="prevent()">
                              <div class="from-input-left">
                                <i
                                  class="el-icon-caret-bottom"
                                  :class="{
                                    'i-active':
                                      itemed.listedThirdly.activeNames &&
                                      childrenItem.orderNumThirdlyPack ==
                                        itemed.listedThirdly.activeNames.filter(
                                          filterItem =>
                                            filterItem ==
                                            childrenItem.orderNumThirdlyPack
                                        )[0],
                                  }"
                                />
                                <div class="edit-input-box">
                                  <div @click.stop="prevent" class="edit-input">
                                    <el-input
                                      v-if="
                                        detail &&
                                        `listedThirdly${index}${indexed}${childrenIndex}` ==
                                          inpClickNum
                                      "
                                      v-model="
                                        childrenItem.templateNameThirdlyPack
                                      "
                                      placeholder="请输入模版名称"
                                      clearable
                                      size="medium"
                                      :id="`indexlistedThirdly${index}${indexed}${childrenIndex}`"
                                      @blur="draggStopFlip('launch')"
                                      @focus="draggStop()"
                                    ></el-input>
                                    <span
                                      v-else
                                      @click.stop="
                                        detailFlip(
                                          `listedThirdly${index}${indexed}${childrenIndex}`
                                        )
                                      "
                                      :class="{
                                        inpactive:
                                          !childrenItem.templateNameThirdlyPack,
                                      }"
                                    >
                                      {{
                                        childrenItem.templateNameThirdlyPack
                                          ? childrenItem.templateNameThirdlyPack
                                          : '点击编辑模版名称'
                                      }}
                                    </span>
                                  </div>
                                  <i
                                    class="el-icon-edit"
                                    @click.stop="
                                      detailFlip(
                                        `listedThirdly${index}${indexed}${childrenIndex}`
                                      )
                                    "
                                    v-if="!lookCollect"
                                  />
                                </div>
                                <div class="edit-input-box">
                                  <div @click.stop="prevent" class="edit-input">
                                    <el-input
                                      v-if="
                                        detail &&
                                        `listedThirdly${index}${indexed}${childrenIndex}templateKeys` ==
                                          inpKeyClickNum
                                      "
                                      v-model="
                                        childrenItem.templateKeyThirdlyPack
                                      "
                                      placeholder="请输入模版Key"
                                      clearable
                                      size="medium"
                                      :id="`indexlistedThirdly${index}${indexed}${childrenIndex}templateKeys`"
                                      @blur="draggStopFlip('launch')"
                                      @focus="draggStop()"
                                    ></el-input>
                                    <span
                                      v-else
                                      @click.stop="
                                        detailFlip(
                                          `listedThirdly${index}${indexed}${childrenIndex}templateKeys`
                                        )
                                      "
                                      :class="{
                                        inpactive:
                                          !childrenItem.templateKeyThirdlyPack,
                                      }"
                                    >
                                      {{
                                        childrenItem.templateKeyThirdlyPack
                                          ? childrenItem.templateKeyThirdlyPack
                                          : '点击编辑模版Key'
                                      }}
                                    </span>
                                  </div>
                                  <i
                                    class="el-icon-edit"
                                    @click.stop="
                                      detailFlip(
                                        `listedThirdly${index}${indexed}${childrenIndex}templateKeys`
                                      )
                                    "
                                    v-if="!lookCollect"
                                  />
                                </div>
                              </div>
                              <div class="fromHeadermenu" v-if="!lookCollect">
                                <i
                                  :class="{
                                    'el-icon-top': true,
                                    'el-icon-disable': childrenIndex == 0,
                                  }"
                                  @click.stop="
                                    operateMenuUp(itemed, childrenIndex)
                                  "
                                ></i>
                                <i
                                  :class="{
                                    'el-icon-bottom': true,
                                    'el-icon-disable':
                                      childrenIndex ==
                                      itemed.listedThirdly.length - 1,
                                  }"
                                  @click.stop="
                                    operateMenuDown(itemed, childrenIndex)
                                  "
                                ></i>
                                <i
                                  class="el-icon-delete-solid"
                                  @click.stop="
                                    operateMenuDelete(itemed, childrenIndex)
                                  "
                                ></i>
                              </div>
                            </div>
                          </template>
                          <!-- 三级子表单 -->
                          <div class="guaranteeSetBottom">
                            <avue-form
                              :option="subformOption"
                              v-model="childrenItem.subFormThirdlyPack"
                            />
                          </div>
                        </el-collapse-item>
                      </transition-group>
                    </el-collapse>
                  </el-collapse-item>
                </transition-group>
              </el-collapse>
            </el-collapse-item>
          </transition-group>
        </el-collapse>
      </div>
      <!-- 增加子表单menu -->
      <div class="subitems-menu-box" v-if="!lookCollect">
        <div class="subitems" @click="addbtn('1')">
          <div class="subadd">
            <i class="el-icon-circle-plus"></i>
            <span>新增单级模块</span>
          </div>
        </div>
        <div class="subitems" @click="addbtn('2')">
          <div class="subadd">
            <i class="el-icon-circle-plus"></i>
            <span>新增二级模块</span>
          </div>
        </div>
        <div class="subitems" @click="addbtn('3')">
          <div class="subadd">
            <i class="el-icon-circle-plus"></i>
            <span>新增三级模块</span>
          </div>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class="updata" v-if="!lookCollect">
        <el-button @click="goBack()">取消</el-button>
        <el-button type="success" v-if="upDateType" @click="upData('up')"
          >上架</el-button
        >
        <el-button type="primary" v-if="upDateType" @click="upData()"
          >保存</el-button
        >
        <el-button type="success" v-if="!upDateType" @click="setData('set')"
          >上架</el-button
        >
        <el-button type="primary" v-if="!upDateType" @click="setData()"
          >保存</el-button
        >
      </div>
    </div>
  </basic-container>
</template>

<script>
// import { set } from 'vue/types/umd'
import {
  getDictionary,
  creditFrom,
  getFromDetail,
  upFromData,
  enable,
} from '@/api/plugin/workflow/externalForm'
// import {mapGetters} from "vuex";

export default {
  data() {
    return {
      id: this.$route.query.id,
      upDateType: false, // 是否编辑
      lookCollect: false, // 是否查看
      draggStart: true,
      draggStartSecond: true,
      draggStartThirdly: true,
      detail: false,
      absolutelyNum: 1,
      inpClickNum: -1,
      inpKeyClickNum: -1,
      activeNames: [],
      dragStartIndex: -1,

      lookId: '',

      information: {
        fromName: '',
        goodsType: '',
        formKey: '',
      },
      option: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 100,
        gutter: 50,
        column: [
          {
            label: '表单名称',
            prop: 'fromName',
            span: 12,
            placeholder: '请输入表单名称',
            disabled: false,
            rules: [
              {
                required: true,
                message: '请输入表单名称',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '表单Key',
            prop: 'formKey',
            span: 12,
            placeholder: '请输入表单Key',
            disabled: false,
            type: 'input',
            rules: [
              {
                required: true,
                message: '请选择流程名称',
                trigger: 'blur',
              },
            ],
            // dicData: [],
          },
          {
            label: '业务类型',
            prop: 'goodsType',
            span: 12,
            placeholder: '请选择业务类型',
            disabled: false,
            type: 'select',
            rules: [
              {
                required: true,
                message: '请选择业务类型',
                trigger: 'change',
              },
            ],
            dicData: [],
          },
        ],
      },
      // element折叠面板组件
      list: [
        // {
        //   grade: '3',
        //   orderNum: '1',
        //   templateName: '1',
        //   listedSecond: [
        //     {
        //       orderNumSecond: 'children1',
        //       templateNameSecond: '1-2',
        //       listedThirdly: [
        //         {
        //           orderNumThirdlyPack: 'childrenPack1',
        //           templateNameThirdlyPack: '1-3',
        //           subFormThirdlyPack: {
        //             dynamic: [
        //               {
        //                 fieldKey: '321',
        //                 fieldNames: '123',
        //               },
        //             ],
        //           },
        //         },
        //       ],
        //     },
        //   ],
        // },
      ],
      subformOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 110,
        typeslot: true,
        column: [
          {
            // label: '子表单',
            labelWidth: 0,
            prop: 'dynamic',
            type: 'dynamic',
            span: 24,
            children: {
              delBtn: true,
              addBtn: true,
              align: 'center',
              headerAlign: 'left',
              rowAdd: done => {
                // this.$message.success('新增回调')
                done({
                  fieldNames: '',
                })
              },
              rowDel: (row, done) => {
                // this.$message.success('删除回调' + JSON.stringify(row))
                done()
              },
              column: [
                {
                  label: '字段名称',
                  prop: 'fieldNames',
                  disabled: false,
                  clearable: false,
                  placeholder: '请输入字段名称',
                  // type: 'select',
                  focus: () => {
                    this.draggStop()
                  },
                  blur: () => {
                    this.draggStopFlip()
                  },
                },
                {
                  label: '字段key',
                  prop: 'fieldKey',
                  disabled: false,
                  clearable: false,
                  placeholder: '请输入字段key',
                  // type: 'select',
                  focus: () => {
                    this.draggStop()
                  },
                  blur: () => {
                    this.draggStopFlip()
                  },
                },
              ],
            },
          },
        ],
      },
      lock: false,
    }
  },
  created() {
    // 业务字典表随时可能变化
    const stringType = 'goods_type'
    getDictionary(stringType).then(res => {
      // 业务类型字典
      const resed = res.data
      if (resed.code == 200) {
        const dicArr = []
        for (const item of resed.data) {
          dicArr.push({
            label: item.dictValue,
            value: Number(item.dictKey),
          })
        }
        this.option.column[2].dicData = dicArr
        if (sessionStorage.getItem('lookCollect') == 'true') {
          // 查看状态将对应组件进行禁用
          this.view(this.id)
          this.lookCollect = true
          for (const item of this.option.column) {
            item.disabled = true
          }
          for (const item of this.subformOption.column[0].children.column) {
            item.disabled = true
          }
          this.subformOption.column[0].children.addBtn = false
          this.subformOption.column[0].children.delBtn = false
        } else if (sessionStorage.getItem('lookCollect') == 'false') {
          // 编辑
          this.upDateType = true
          this.view(this.id)
        }
      }
    })
  },
  mounted() {
    // 清除组件默认class样式
    document.querySelector('.el-card__body').classList.remove('el-card__body')
  },
  methods: {
    draggStop() {
      // 阻止输入时的拖拽
      this.draggStart = false
      this.draggStartSecond = false
      this.draggStartThirdly = false
    },
    draggStopFlip(launch) {
      // 输入失焦的拖拽的阻止解除
      if (launch) {
        // 将title的点击编辑模板变成input输入框
        this.detail = !this.detail
      }
      this.draggStart = true
      this.draggStartSecond = true
      this.draggStartThirdly = true
    },
    dropCard() {
      // 释放拖拽后将三个拖拽状态重置
      this.draggStart = true
      this.draggStartSecond = true
      this.draggStartThirdly = true
    },
    dragOverCard(e) {
      e.preventDefault()
    },
    dragEnterCard(e, item, index, type) {
      event.stopPropagation()
      e.preventDefault()
      // 避免源对象触发自身的dragenter事件
      if (this.dragStartIndex !== index && !this.lock) {
        this.lock = true
        if (type == 'first') {
          // 将单级的两个数据互换
          const source = this.list[this.dragStartIndex]
          this.list.splice(this.dragStartIndex, 1)
          this.list.splice(index, 0, source)
        } else if (type == 'second') {
          // 将二级模块的两个数据互换
          const source = item.listedSecond[this.dragStartIndex]
          item.listedSecond.splice(this.dragStartIndex, 1)
          item.listedSecond.splice(index, 0, source)
        } else if (type == 'thirdly') {
          // 将三级模块的两个数据互换
          const source = item.listedThirdly[this.dragStartIndex]
          item.listedThirdly.splice(this.dragStartIndex, 1)
          item.listedThirdly.splice(index, 0, source)
        }
        // 排序变化后目标对象的索引变成源对象的索引
        this.dragStartIndex = index
        setTimeout(() => {
          this.lock = false
        }, 100)
      }
    },
    dragStartCard(e, type, indexed) {
      // ev.preventDefault()
      this.dragStartIndex = indexed
      if (type == 'first') {
        // 单级模块拖动将二级模块得拖动锁定
        this.draggStartSecond = false
        this.draggStartThirdly = false
      } else if (type == 'second') {
        // 二级模块拖动将单级模块得拖动锁定
        this.draggStart = false
        this.draggStartThirdly = false
      } else if (type == 'thirdly') {
        // 三级模块拖动将单级模块得拖动锁定
        this.draggStart = false
        this.draggStartSecond = false
      }
    },
    swapArr(Darr, index1, index2) {
      /*数组两个元素位置互换*/
      Darr[index1] = Darr.splice(index2, 1, Darr[index1])[0]
      return Darr
    },
    swapObj(sourceObj, sourceKey, targetObj, targetKey) {
      /*对象两个属性位置互换*/
      let temp = sourceObj[sourceKey]
      sourceObj[sourceKey] = targetObj[targetKey]
      targetObj[sourceKey] = temp
      return sourceObj
    },
    operateMenuPush(itemed) {
      // 二级模块的title点击新增事件
      if (itemed.listedSecond) {
        const num = itemed.listedSecond.length
        const listRootData = {
          orderNumSecond: `children${num + 1}`,
          templateNameSecond: '',
        }
        // 三级模块的title点击新增事件
        if (itemed.grade === '3') {
          listRootData['listedThirdly'] = [
            {
              orderNumThirdlyPack: 'childrenPack1',
              templateNameThirdlyPack: '',
            },
          ]
        }
        itemed.listedSecond.push(listRootData)
      } else if (itemed.listedThirdly) {
        // 三级模块的子 title点击新增事件
        const num = itemed.listedThirdly.length
        const listRootDataed = {
          orderNumThirdlyPack: `childrenPack${num + 1}`,
          templateNameThirdlyPack: '',
        }
        itemed.listedThirdly.push(listRootDataed)
      }
    },
    operateMenuUp(item, index, type) {
      // 上移动
      if (index == 0) return
      if (type !== 'second' && item.grade) {
        // 单级移动
        this.list = this.swapArr(this.list, index, index - 1)
      } else if (type === 'second') {
        // 二级模块移动
        item.listedSecond = this.swapArr(item.listedSecond, index, index - 1)
      } else if (item.listedThirdly) {
        // 三级模块移动
        item.listedThirdly = this.swapArr(item.listedThirdly, index, index - 1)
      }
    },
    operateMenuDown(item, index, type) {
      if (type !== 'second' && item.grade) {
        // 单级移动
        if (this.list.length == index + 1) return
        this.list = this.swapArr(this.list, index, index + 1)
      } else if (type === 'second') {
        // 二级模块移动
        if (item.listedSecond.length == index + 1) return
        item.listedSecond = this.swapArr(item.listedSecond, index, index + 1)
      } else if (item.listedThirdly) {
        // 三级模块移动
        if (item.listedThirdly.length == index + 1) return
        item.listedThirdly = this.swapArr(item.listedThirdly, index, index + 1)
      }
    },
    operateMenuDelete(item, index, type) {
      if (type !== 'second' && item.grade) {
        // 单级删除
        this.list.splice(index, 1)
      } else if (type === 'second') {
        // 二级模块删除
        item.listedSecond.splice(index, 1)
      } else if (item.listedThirdly) {
        // 三级模块删除
        item.listedThirdly.splice(index, 1)
      }
    },
    setData(type) {
      // 保存前校验
      this.$refs.basicInformation.validate((valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0][0].message
          if (!errMsg) {
            errMsg = '必填项未填'
          }
          this.$message.error(errMsg)
        } else {
          const externalList = []
          for (const item of this.list) {
            if (item.grade === '3') {
              const listedSecondData = []
              // 处理二级折叠面板数据
              for (const itemed of item.listedSecond) {
                const listedThirdlyData = []
                // 处理三级折叠面板数据
                for (const itemThirdly of itemed.listedThirdly) {
                  const dynamicData = []
                  // 处理三级子表单数据
                  for (const itemDynamic of itemThirdly.subFormThirdlyPack
                    .dynamic) {
                    dynamicData.push({
                      name: itemDynamic.fieldNames,
                      fieldKey: itemDynamic.fieldKey,
                    })
                  }
                  listedThirdlyData.push({
                    name: itemThirdly.templateNameThirdlyPack,
                    templateKey: itemThirdly.templateKeyThirdlyPack,
                    templateLabel: itemThirdly.orderNumThirdlyPack,
                    externalFormTemplateFieldList: dynamicData,
                  })
                }
                listedSecondData.push({
                  name: itemed.templateNameSecond,
                  templateKey: itemed.templateKeySecond,
                  templateLabel: itemed.orderNumSecond,
                  children: listedThirdlyData,
                })
              }
              externalList.push({
                name: item.templateName,
                templateKey: item.templateKey,
                sort: item.grade,
                templateLabel: item.orderNum,
                children: listedSecondData,
              })
            } else if (item.grade === '2') {
              const listedSecondData = []
              // 处理二级折叠面板数据
              for (const itemed of item.listedSecond) {
                const dynamicData = []
                // 处理二级子表单数据
                for (const itemDynamic of itemed.subFormSecond.dynamic) {
                  dynamicData.push({
                    name: itemDynamic.fieldNames,
                    fieldKey: itemDynamic.fieldKey,
                  })
                }
                listedSecondData.push({
                  name: itemed.templateNameSecond,
                  templateKey: itemed.templateKeySecond,
                  templateLabel: itemed.orderNumSecond,
                  externalFormTemplateFieldList: dynamicData,
                })
              }
              externalList.push({
                name: item.templateName,
                templateKey: item.templateKey,
                sort: item.grade,
                templateLabel: item.orderNum,
                children: listedSecondData,
              })
            } else if (item.grade === '1') {
              const dynamicData = []
              // 处理单级子表单数据
              for (const itemDynamic of item.subForm.dynamic) {
                dynamicData.push({
                  name: itemDynamic.fieldNames,
                  fieldKey: itemDynamic.fieldKey,
                })
              }
              externalList.push({
                name: item.templateName,
                templateKey: item.templateKey,
                sort: item.grade,
                templateLabel: item.orderNum,
                externalFormTemplateFieldList: dynamicData,
              })
            }
          }
          // 新增数据
          const data = {
            name: this.information.fromName,
            businessType: this.information.goodsType,
            formKey: this.information.formKey,
            externalFormTemplateList: externalList,
          }
          // 新增请求
          creditFrom(data).then(res => {
            const resData = res.data
            if (resData.code == 200) {
              if (type) {
                this.enable(resData.data) // 上架
              } else {
                this.$message.success('保存成功')
              }
              this.id = resData.data
              this.upDateType = true
              sessionStorage.setItem('lookCollect', 'false')
            }
          })
        }
        done()
      })
    },
    upData(type) {
      // 更新前校验
      this.$refs.basicInformation.validate((valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0][0].message
          if (!errMsg) {
            errMsg = '必填项未填'
          }
          this.$message.error(errMsg)
        } else {
          const externalList = []
          for (const item of this.list) {
            if (item.grade === '3') {
              const listedSecondData = []
              // 处理二级折叠面板数据
              for (const itemed of item.listedSecond) {
                const listedThirdlyData = []
                // 处理三级折叠面板数据
                for (const itemThirdly of itemed.listedThirdly) {
                  const dynamicData = []
                  // 处理三级子表单数据
                  for (const itemDynamic of itemThirdly.subFormThirdlyPack
                    .dynamic) {
                    dynamicData.push({
                      name: itemDynamic.fieldNames,
                      fieldKey: itemDynamic.fieldKey,
                    })
                  }
                  listedThirdlyData.push({
                    name: itemThirdly.templateNameThirdlyPack,
                    templateKey: itemThirdly.templateKeyThirdlyPack,
                    templateLabel: itemThirdly.orderNumThirdlyPack,
                    externalFormTemplateFieldList: dynamicData,
                  })
                }
                listedSecondData.push({
                  name: itemed.templateNameSecond,
                  templateKey: itemed.templateKeySecond,
                  templateLabel: itemed.orderNumSecond,
                  children: listedThirdlyData,
                })
              }
              externalList.push({
                name: item.templateName,
                templateKey: item.templateKey,
                sort: item.grade,
                templateLabel: item.orderNum,
                children: listedSecondData,
              })
            } else if (item.grade === '2') {
              const listedSecondData = []
              // 处理二级折叠面板数据
              for (const itemed of item.listedSecond) {
                const dynamicData = []
                // 处理二级子表单数据
                for (const itemDynamic of itemed.subFormSecond.dynamic) {
                  dynamicData.push({
                    name: itemDynamic.fieldNames,
                    fieldKey: itemDynamic.fieldKey,
                  })
                }
                listedSecondData.push({
                  name: itemed.templateNameSecond,
                  templateKey: itemed.templateKeySecond,
                  templateLabel: itemed.orderNumSecond,
                  externalFormTemplateFieldList: dynamicData,
                })
              }
              externalList.push({
                name: item.templateName,
                templateKey: item.templateKey,
                sort: item.grade,
                templateLabel: item.orderNum,
                children: listedSecondData,
              })
            } else if (item.grade === '1') {
              const dynamicData = []
              // 处理单级子表单数据
              for (const itemDynamic of item.subForm.dynamic) {
                dynamicData.push({
                  name: itemDynamic.fieldNames,
                  fieldKey: itemDynamic.fieldKey,
                })
              }
              externalList.push({
                name: item.templateName,
                templateKey: item.templateKey,
                sort: item.grade,
                templateLabel: item.orderNum,
                externalFormTemplateFieldList: dynamicData,
              })
            }
          }
          // 更新数据
          const data = {
            name: this.information.fromName,
            businessType: this.information.goodsType,
            formKey: this.information.formKey,
            externalFormTemplateList: externalList,
            id: this.id,
          }
          // 更新请求
          upFromData(data).then(res => {
            if (res.data.code == 200) {
              if (type) {
                this.enable(this.id) // 上架
              } else {
                this.$message.success('保存成功')
              }
            }
          })
        }
        done()
      })
    },

    goBack() {
      this.$router.$avueRouter.closeTag()
      this.$router.push({ path: '/workflow/externalform' })
    },
    goToBackList() {
      sessionStorage.setItem('lookCollect', '')
      this.$router.$avueRouter.closeTag()
      this.$router.push({ path: '/workflow/externalform' })
    },
    view(viewId) {
      // 查询数据详情
      getFromDetail({ id: viewId }).then(res => {
        const resData = res.data
        const dataed = resData.data
        if (resData.code == 200 && dataed) {
          for (const item of dataed.externalFormTemplateList) {
            if (item.sort === 3) {
              const listedSecondList = []
              // 处理二级折叠面板数据
              for (const itemed of item.children) {
                const listedThirdlyList = []
                // 处理三级折叠面板数据
                for (const itemThirdly of itemed.children) {
                  const dynamicList = []
                  // 处理三级子表单数据
                  for (const itemDynamic of itemThirdly.externalFormTemplateFieldList) {
                    dynamicList.push({
                      fieldNames: itemDynamic.name,
                      fieldKey: itemDynamic.fieldKey,
                    })
                  }
                  listedThirdlyList.push({
                    orderNumThirdlyPack: itemThirdly.templateLabel,
                    templateNameThirdlyPack: itemThirdly.name,
                    templateKeyThirdlyPack: itemThirdly.templateKey,
                    subFormThirdlyPack: {
                      dynamic: dynamicList,
                    },
                  })
                }
                listedSecondList.push({
                  orderNumSecond: itemed.templateLabel,
                  templateNameSecond: itemed.name,
                  templateKeySecond: itemed.templateKey,
                  listedThirdly: listedThirdlyList,
                })
              }
              this.list.push({
                grade: String(item.sort),
                orderNum: item.templateLabel,
                templateName: item.name,
                templateKey: item.templateKey,
                listedSecond: listedSecondList,
              })
            } else if (item.sort === 2) {
              const listedSecondList = []
              // 处理二级折叠面板数据
              for (const itemed of item.children) {
                const dynamicList = []
                // 处理二级子表单数据
                for (const itemDynamic of itemed.externalFormTemplateFieldList) {
                  dynamicList.push({
                    fieldNames: itemDynamic.name,
                    fieldKey: itemDynamic.fieldKey,
                  })
                }
                listedSecondList.push({
                  orderNumSecond: itemed.templateLabel,
                  templateNameSecond: itemed.name,
                  templateKeySecond: itemed.templateKey,
                  subFormSecond: {
                    dynamic: dynamicList,
                  },
                })
              }
              this.list.push({
                grade: String(item.sort),
                orderNum: item.templateLabel,
                templateName: item.name,
                templateKey: item.templateKey,
                listedSecond: listedSecondList,
              })
            } else if (item.sort === 1) {
              const dynamicList = []
              // 处理单级子表单数据
              for (const itemDynamic of item.externalFormTemplateFieldList) {
                dynamicList.push({
                  fieldNames: itemDynamic.name,
                  fieldKey: itemDynamic.fieldKey,
                })
              }
              this.list.push({
                grade: String(item.sort),
                orderNum: item.templateLabel,
                templateName: item.name,
                templateKey: item.templateKey,
                subForm: {
                  dynamic: dynamicList,
                },
              })
            }
          }
          this.information.fromName = dataed.name
          this.information.goodsType = dataed.businessType
          this.information.formKey = dataed.formKey
        }
      })
    },
    enable(viewId) {
      enable(viewId).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          console.log(resData)
          this.$message.success('已上架')
          this.goToBackList()
        }
      })
    },
    prevent() {
      // 用于阻止elm组件事件冒泡
    },
    detailFlip(index) {
      if (this.lookCollect) return
      this.inpClickNum = index
      this.inpKeyClickNum = index
      this.detail = !this.detail
      setTimeout(() => {
        document.getElementById(`index${index}`).focus()
        // 让input得焦
      }, 300)
    },
    addbtn(num) {
      // 默认push
      const listData = {
        orderNum: `${this.list.length + 1}`,
        templateName: '',
        templateKey: '',
        grade: '1', // 默认新增单级模块
      }
      if (num == '2') {
        // 二级模块push
        listData['grade'] = '2'
        const listRootData = [
          {
            orderNumSecond: 'children1',
            templateNameSecond: '',
            templateKeySecond: '',
          },
        ]
        listData['listedSecond'] = listRootData
      } else if (num == '3') {
        // 三级模块push
        listData['grade'] = '3'
        const listRootDataed = [
          {
            orderNumSecond: 'children1',
            templateNameSecond: '',
            templateKeySecond: '',
            listedThirdly: [
              {
                orderNumThirdlyPack: 'childrenPack1',
                templateNameThirdlyPack: '',
                templateKeyThirdlyPack: '',
              },
            ],
          },
        ]
        listData['listedSecond'] = listRootDataed
      }

      this.list.push(listData)
    },
  },
}
</script>

<style lang="scss">
.external-form {
  // 以下为collapse的样式修改
  .el-collapse {
    border-top: none;
    border-bottom: none;
  }
  .el-collapse-item__arrow {
    display: none;
  }
  .el-collapse-item__header {
    height: 55px;
    border-bottom: none;
  }
  .el-collapse-item__content {
    padding-bottom: 0 !important;
  }
  .el-collapse-item__wrap {
    border-bottom: none;
  }
  .avue-form__menu {
    display: none;
  }
  .el-input.is-disabled .el-input__inner {
    color: #000;
  }

  $bg-color: #f3f3f3;
  $bg-tcolor: #fff;
  // 非组件类样式修改
  background: $bg-color;
  padding: 20px;
  header {
    padding: 20px;
    background: $bg-tcolor;
    border-radius: 10px;
    overflow: hidden;

    .title-top {
      width: 100%;
      height: 24px;
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-bold;
      background: $bg-tcolor;

      &::before {
        content: '';
        display: inline-block;
        width: 8px;
        height: 16px;
        line-height: 20px;
        border-radius: 15px;
        background-color: rgba(18, 119, 255, 100);
        text-align: center;
        transform: translateY(2px);
        box-sizing: border-box;
        margin-right: 4px;
      }
    }
    .guaranteeSetTop {
      border: 2px solid RGB(245, 245, 245);
      padding-top: 27px;
      box-sizing: border-box;
      margin-bottom: 20px;
      background: $bg-tcolor;
      border-radius: 10px;
      overflow: hidden;
    }
  }
  .module-box {
    .drag-list {
      border-radius: 7px;
      box-sizing: border-box;
      overflow: hidden;
      .drag-move {
        transition: transform 0.3s;
      }

      .fromCollapse {
        margin-top: 2%;
        box-sizing: border-box;
        overflow: hidden;
        border-radius: 10px;

        &:last-child {
          margin-bottom: 2%;
        }

        .fromHeader {
          width: 100%;
          font-size: 16px;
          font-family: SourceHanSansSC-bold;
          background-color: $bg-tcolor;
          color: rgba(16, 16, 16, 100);
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          padding: 0 20px;
          overflow: hidden;

          .from-input-left {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 60%;

            .edit-input-box {
              min-width: 280px;
              display: flex;
              justify-content: flex-start;
              align-items: center;

              &:last-child {
                margin-left: 10px;
              }

              .edit-input {
                margin-right: 3%;

                .inpactive {
                  color: rgba(125, 125, 125, 100);
                  font-size: 16px;
                  font-family: SourceHanSansSC-bold;
                }
              }

              i.el-icon-edit {
                font-size: 150%;
                color: rgba(105, 124, 255, 100);
              }
            }

            i.el-icon-caret-bottom {
              font-size: 150%;
              transform: rotate(-90deg);
              transition: transform 0.4s;
            }

            .i-active {
              transform: rotate(0deg) !important;
            }
          }

          .fromHeadermenu {
            position: relative;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: space-between;

            i {
              margin-right: 12px;

              &:last-child {
                margin-right: 0;
              }
            }

            i.el-icon-top {
              font-size: 150%;
              color: rgba(68, 68, 68, 100);
              font-weight: bolder;
            }
            i.el-icon-bottom {
              font-size: 150%;
              color: rgba(68, 68, 68, 100);
              font-weight: bolder;
            }
            i.el-icon-plus {
              font-size: 135%;
              color: rgba(105, 124, 255, 100);
              font-weight: bolder;
            }
            i.el-icon-delete-solid {
              font-size: 135%;
              color: rgba(255, 77, 54, 100);
            }
            i.el-icon-disable {
              color: rgba(167, 166, 166, 100);
            }
          }
        }
      }
      .from-collapse-children {
        margin: 15px 20px 0;
        background-color: #f7f7f7;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 15px;
        }

        .el-collapse-item__header {
          background-color: transparent;
        }
        .el-collapse-item__wrap {
          background-color: transparent;
        }

        .fromHeader {
          background-color: #f7f7f7;
          border-radius: 10px;
        }
      }
      .from-collapse-childrenThirdly {
        background-color: #fff;
        margin: 15px 20px 0;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 15px;
        }

        .fromHeader {
          background-color: transparent;
          border-radius: 10px;
        }
      }
    }
    .guaranteeSetBottom {
      border-radius: 10px;
      padding: 20px 30px 0 30px;
      box-sizing: border-box;

      .avue-form__group {
        & > div:last-child {
          display: none;
        }
      }
    }
  }
  .subitems-menu-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5%;

    .subitems {
      width: 100%;
      height: 48px;
      border-radius: 31px;
      background-color: rgba(255, 255, 255, 100);
      text-align: center;
      background: $bg-tcolor;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-right: 24px;

      &:last-child {
        margin-right: 0;
      }

      & .subadd {
        // width: 85px;
        height: 24px;
        color: rgba(105, 124, 255, 100);
        font-size: 16px;
        font-family: SourceHanSansSC-bold;
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          word-break: keep-all;
          margin-left: 3px;
        }

        i.el-icon-circle-plus {
          color: rgba(105, 124, 255, 100);
        }
      }
    }
  }
  .updata {
    position: relative;
    box-sizing: border-box;
    text-align: right;
    margin-top: 1%;
    min-height: 60px;
    line-height: 60px;
    margin-bottom: 0.5%;
  }
}
</style>
