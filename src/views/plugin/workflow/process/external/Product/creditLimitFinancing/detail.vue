<template>
  <div class="creditLimitFinancing">
    <basic-container>
      <avue-skeleton v-if="waiting" loading avatar :rows="8" />
      <template v-else>
        <avue-affix id="avue-view" :offset-top="114">
          <div class="header">
            <avue-title :value="process.processDefinitionName || '申请额度-融资产品'"></avue-title>
          </div>
        </avue-affix>
        <el-tabs v-model="activeName">
          <el-tab-pane label="申请信息" name="first">
            <div class="apply-container">
              <div class="left">
                <div class="form-item">
                  <span class="title">任务编号：</span>
                  <span class="value">{{ variables.processNo }}</span>
                </div>
                <div class="form-item">
                  <span class="title">申请人：</span>
                  <span class="value">
                    <span style="color: rgba(105, 124, 255, 100)">{{ variables.applyUserName }}</span>
                  </span>
                </div>
                <div class="form-item">
                  <span class="title">申请时间：</span>
                  <span class="value">{{ resData.createTime }}</span>
                </div>
              </div>
              <div class="right">
                <!-- <svg-icon class="" /> -->
                <div class="right-icon-box" v-if="resData.status == 'todo'">
                  <i class="el-icon-time" style="color: #4e9bfc; font-size: 40px" />
                  <span class="status">待审批</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'finished'">
                  <i class="el-icon-circle-check" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已完成</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'done'">
                  <i class="el-icon-time" style="color: #3dc861; font-size: 40px" />
                  <span class="status">审批中</span>
                </div>
                <div class="right-icon-box" v-else-if="resData.status == 'terminate'">
                  <i class="icon-line-tixing" style="color: #3dc861; font-size: 40px" />
                  <span class="status">已终止</span>
                </div>
                <div class="desc">
                  <div v-if="resData.status != 'finished'">
                    <span> 当前任务处于【{{ resData.taskName }}】节点， </span>
                    <span class="target" style="color: rgba(105, 124, 255, 100)">
                      <span style="color: rgba(16, 16, 16, 100)">【</span>
                      {{ resData.assigneeName }}
                      <span style="color: rgba(16, 16, 16, 100)">】</span>
                    </span>
                    <span>正在进行审批</span>
                  </div>
                  <span v-else>当前任务已审批成功</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="流转信息" name="second">
            <wf-flow :flow="flow" style="margin-top: 5px"></wf-flow>
          </el-tab-pane>
          <el-tab-pane label="流程跟踪" name="third">
            <template v-if="activeName == 'third'">
              <wf-design ref="bpmn" style="height: 500px; margin-top: 5px" :options="bpmnOption"></wf-design>
            </template>
          </el-tab-pane>
        </el-tabs>
      </template>
    </basic-container>

    <template v-if="!waiting && activeName == 'first'">
      <!-- 申请产品 -->
      <basic-container v-if="modeReadable.applyProduct">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">申请产品</h1>
            <div class="boxs-to-apply-for-product-title">
              <div class="boxs-to-apply-for-product-left-logo">
                <div class="boxs-to-apply-for-product-left-logo-box">
                  <div class="boxs-to-apply-for-product-left-logo-box-img">
                    <img :src="processGoodsObj.capitalLogo" alt="" />
                  </div>
                  <div class="boxs-to-apply-for-product-left-logo-box-goodname">
                    <span @click="viewGoods()">{{ processGoodsObj.goodsName }}</span>
                    <span>{{ processGoodsObj.capitalName }}</span>
                  </div>
                </div>
              </div>
              <span class="boxs-to-apply-for-product-right-goodtype">{{
                processGoodsObj.type == 1
                  ? '应收账款质押'
                  : processGoodsObj.type == 2
                  ? '代采融资'
                  : processGoodsObj.type == 5
                  ? '订单融资'
                  : '云信'
              }}</span>
            </div>
            <div class="descriptions-for-box">
              <el-descriptions title="" :column="tableData.length < 3 ? 2 : 3" border>
                <el-descriptions-item v-for="item in tableData" :key="item.id" :label="item.label">{{
                  item.value
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 补充资料文本 -->
      <div style="margin-top: 10px" v-if="modeReadable.furtherInformationText">
        <basic-container v-for="(item, index) in customerMaterialList" :key="index">
          <el-collapse v-model="activeNames" @change="handleChange()">
            <el-collapse-item :name="item.orderNum">
              <!-- title的solt -->
              <template slot="title">
                <div class="fromHeader">
                  <div class="fromLeft">
                    <i
                      class="el-icon-caret-bottom"
                      :class="{
                        'i-active': item.orderNum == activeNames.filter(filterItem => filterItem == item.orderNum)[0],
                      }"
                    ></i>
                    <h1 class="fromLeft-title">{{ item.templateName }}</h1>
                  </div>
                </div>
              </template>
              <!-- 展示表单 -->
              <div class="descriptions-for-box">
                <el-descriptions title="" :column="3" border>
                  <el-descriptions-item
                    v-for="(itemed, indexed) in item.creditFromFields"
                    :key="indexed"
                    :label="itemed.fieldDesc"
                  >
                    <span v-if="![4, 5, 6].includes(itemed.dataType)">
                      {{ itemed.value }}
                    </span>
                    <span v-else-if="[5, 6].includes(itemed.dataType)">
                      {{ itemed.value }}
                      <template v-if="itemed.value">
                        {{ itemed.dataType === 5 ? '元' : '万元' }}
                      </template>
                    </span>
                    <span v-else>
                      {{ itemed.timeValue }}
                    </span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-collapse-item>
          </el-collapse>
        </basic-container>
      </div>

      <!-- 补充资料附件 -->
      <basic-container v-if="modeReadable.furtherInformationAccessory">
        <el-collapse v-model="activeNames3" @change="handleChange3">
          <el-collapse-item name="furtherInformation">
            <!-- title的solt -->
            <template slot="title">
              <div class="fromHeader">
                <div class="fromLeft">
                  <i
                    class="el-icon-caret-bottom"
                    :class="{
                      'i-active': change3Type,
                    }"
                  ></i>
                  <h1 class="fromLeft-title">补充资料</h1>
                </div>
              </div>
            </template>
            <!-- 展示表单 -->
            <FilePreviewHWP :formUpload="customerMaterialFormUpload" />
          </el-collapse-item>
        </el-collapse>
      </basic-container>

      <!-- 补充信息 -->
      <basic-container v-if="modeReadable.supplementaryInformation">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">补充信息</h1>
            <div style="margin-top: 10px">
              <el-tabs v-model="activeNameed" type="card">
                <el-tab-pane :key="item.name" :name="item.name" v-for="(item, index) in supplementaryInformation">
                  <!-- tab插槽 start -->
                  <template slot="label">
                    <el-button-group>
                      <el-button
                        type="primary"
                        size="medium"
                        :plain="item.name == activeNameed ? false : true"
                        :class="{
                          'bord-radius-left': index == 0,
                          'bord-radius-right': index == supplementaryInformation.length - 1,
                        }"
                        :style="{
                          color: item.errorM && item.name != activeNameed ? 'red' : '',
                        }"
                        >{{ item.title }}</el-button
                      >
                    </el-button-group>
                  </template>
                  <!-- tab插槽 end -->
                  <!-- 价值分析 start -->
                  <div v-if="item.name == 'supplementaryInformation_valueAnalysis'">
                    <avue-form ref="valueAnalysisRef" :option="valueAnalysisOption" v-model="formControl"> </avue-form>
                  </div>
                  <!-- 价值分析 end -->
                  <!-- 价值分析AI start -->
                  <div v-else-if="item.name == 'supplementaryInformation_valueAnalysis_AI'">
                    <avue-form ref="valueAnalysisRefAI" :option="valueAnalysisOptionAI" v-model="formControl"> 
                      <!-- <template slot-scope="{ row }" slot="valueAnalysis_pdf">
                        <el-button type="primary" size="mini" @click="handleClick(1)">查看报告</el-button>
                      </template> -->
                    </avue-form>
                  </div>
                  <!-- 价值分析AI end -->
                  <!-- 征信信息 start -->
                  <div v-else-if="item.name == 'supplementaryInformation_creditInformation'">
                    <avue-form ref="creditInformationRef" :option="creditInformationOption" v-model="formControl">
                    </avue-form>
                  </div>
                  <!-- 征信信息 end -->
                  <!-- 征信信息AI start -->
                  <div v-else-if="item.name == 'supplementaryInformation_creditInformation_AI'">
                    <avue-form ref="creditInformationRefAI" :option="creditInformationOptionAI" v-model="formControl">
                      <!-- <template slot-scope="{ row }" slot="creditInformation_pdf">
                        <el-button type="primary" size="" @click="handleClick(2)">查看报告</el-button>
                      </template> -->
                    </avue-form>
                  </div>
                  <!-- 征信信息AI end -->
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </div>
        <div class="button-box" style="text-align: right;">
          <el-button
            size="small"
            icon="el-icon-view"
            @click="handleClick"
            type="primary"
            v-if="
              activeNameed === 'supplementaryInformation_valueAnalysis_AI' &&
              isValueAnalysis_pdf_btn &&
              valueAnalysis_pdf
            "
          >
            {{ btnName }}报告
          </el-button>
          <el-button
            size="small"
            icon="el-icon-view"
            @click="handleClick"
            type="primary"
            v-if="
              activeNameed === 'supplementaryInformation_creditInformation_AI' &&
              isCreditInformation_pdf_btn &&
              creditInformation_pdf"
          >
            {{ btnName }}报告
          </el-button>
        </div>
      </basic-container>

      <!-- 合同签署 -->
      <basic-container v-if="modeReadable.contractSigning">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1">合同签署</h1>
            <div class="boxs-to-apply-for-product-body">
              <el-table :data="tableData2" :max-height="240" style="width: 100%; border: 1px solid #ebeef5">
                <el-table-column
                  v-if="tableData2Readable.contractSigning_serialNumber"
                  prop="serial"
                  label="序号"
                  width="80"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_contractNumber"
                  prop="contractId"
                  label="合同编号"
                  width="300"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_contractTitle"
                  prop="contractTitle"
                  label="合同标题"
                  width="300"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_creationTime"
                  prop="createTime"
                  label="创建时间"
                  width="250"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_signTheState"
                  prop="statusText"
                  label="签署状态"
                  min-width="150"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-tag type="success">{{ scope.row.statusText }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="tableData2Readable.contractSigning_operation"
                  prop="firstTradeTime"
                  label="操作"
                  min-width="100"
                  align="center"
                >
                  <template slot-scope="scope">
                    <div style="font-weight: unset">
                      <span class="view" @click="viewContract(scope)">预览</span>
                      <span class="down" @click="downContract(scope)">下载</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </basic-container>

      <!-- 风控信息 -->
      <basic-container v-if="modeReadable.riskControlInformation">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1" style="margin-bottom: 15px">风控信息</h1>
            <RiskControlInformation
              ref="riskControlForm"
              @childByValue="childByValue"
              @validataFunction="validataFunction"
              :id="variables.ratingRecordId"
              :taskForm="taskForm"
              :variables="variables"
              :writeableType="false"
              :isCoreEnterprise="isCoreEnterprise"
            />
          </div>
        </div>
      </basic-container>

      <basic-container v-if="bankAccountInfo">
        <div class="boxs">
          <div class="boxs-to-apply-for-product">
            <h1 class="boxs-to-apply-for-product-h1" style="margin-bottom: 15px">{{ bankAccountInfoName }}</h1>
            <avue-form ref="bankAccountInfoRef" :option="bankAccountInfoOption" v-model="formControl"> </avue-form>
          </div>
        </div>
      </basic-container>
    </template>

    <!-- 征信pdf -->
    <FilePreview :url="pdfSrc" />
  </div>
</template>

<script>
import WfButton from '@/views/plugin/workflow/process/components/button.vue'
import WfFlow from '@/views/plugin/workflow/process/components/flow.vue'
import customExForm from '@/views/plugin/workflow/mixins/custom-ex-form'
import RiskControlInformation from '@/views/plugin/workflow/process/components/riskControlInformation.vue'
import FilePreviewHWP from './component/preview-documents/index.vue'
import {
  getByContractId,
  contractDownload,
  skipToPreview,
  getDictionary,
  customerBankCardPerson,
  getExtractResult
} from '@/api/goods/pcontrol/workflow/productConfirmation'
import { goodsTypeToPath } from '../globalFun.js'
import { formatMoney2, numFormat } from '@/util/filter'

export default {
  mixins: [customExForm],
  components: { WfButton, WfFlow, FilePreviewHWP, RiskControlInformation },
  watch: {
    '$route.params.params': {
      handler(val) {
        if (val) {
          const param = JSON.parse(Buffer.from(val, 'base64').toString())
          const { taskId, processInsId } = param
          if (taskId && processInsId) this.getDetail(taskId, processInsId)
        }
      },
      immediate: true,
    },
    activeNameed(newData, oldData) {
      if (newData == oldData) return
      if (newData != '') {
        this.supplementaryInformation = this.supplementaryInformation.map(item => {
          if (item.name == newData) {
            item.errorM = false
          }
          return item
        })
      }
    },
  },
  data() {
    return {
      activeName: 'first',
      defaults: {},
      form: {},
      vars: [], // 需要提交的字段
      submitLoading: false, // 提交时按钮loading
      comment: '',
      resData: {},
      variables: {},
      taskForm: {},
      objData: {},
      processGoodsObj: {},
      tableData: [],
      customerMaterialList: [],
      customerMaterialFormUpload: [],
      activeNames: [1],
      changeType: true,
      activeNames3: ['furtherInformation'],
      change3Type: true,
      activeNameed: 'supplementaryInformation_valueAnalysis_AI',
      supplementaryInformation: [],
      supplementaryInformationWriter: [],
      supplementaryInformationData: [
        {
          title: '价值分析AI',
          name: 'supplementaryInformation_valueAnalysis_AI',
          refed: 'valueAnalysisRefAI',
        },
        {
          title: '价值分析',
          name: 'supplementaryInformation_valueAnalysis',
          refed: 'valueAnalysisRef',
        },
        {
          title: '征信信息AI',
          name: 'supplementaryInformation_creditInformation_AI',
          refed: 'creditInformationRefAI',
        },
        {
          title: '征信信息',
          name: 'supplementaryInformation_creditInformation',
          refed: 'creditInformationRef',
        },
      ],
      formControl: {},
      valueAnalysisOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 140,
        gutter: 50,
        column: [],
      },
      valueAnalysisOptionAI: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 160,
        gutter: 50,
        column: [],
      },
      creditInformationOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 185,
        gutter: 50,
        column: [],
      },
      creditInformationOptionAI: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 185,
        gutter: 50,
        column: [],
      },
      tableData2: [],
      tableData2Readable: {
        contractSigning_serialNumber: false,
        contractSigning_contractNumber: false,
        contractSigning_contractTitle: false,
        contractSigning_creationTime: false,
        contractSigning_signTheState: false,
        contractSigning_operation: false,
      },
      // 一级大模块显影控制
      modeReadable: {},
      lock: false,
      isCoreEnterprise: false, // 是否核心企业

      bankAccountInfo: false, // 银行账户信息
      bankAccountInfoName: "", // 银行账户信息
      bankAccountInfoOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 140,
        gutter: 50,
        column: [],
      },

      pdfSrc: '',
      creditInformation_pdf: '',
      valueAnalysis_pdf: '',
      isValueAnalysis_pdf_btn: false,
      isCreditInformation_pdf_btn: false,
    }
  },
  computed: {
    btnName() {
      const reportNamesMap = {
        'supplementaryInformation_valueAnalysis_AI': '价值分析',
        'supplementaryInformation_creditInformation_AI': '征信分析',
      }
      return reportNamesMap[this.activeNameed] || '查看'
    }
  },
  methods: {
    // 价值分析AI
    async getExtractResult(processInstanceId, businessType) {
      const { data: { data } } = await getExtractResult({ processInstanceId, businessType })
      return data
    },
    // 征信信息AI
    async getExtractResult2(processInstanceId, businessType) {
      const { data: { data } } = await getExtractResult({ processInstanceId, businessType: businessType + 1 })
      return data
    },
    filterBox(arr, condition) {
      return arr.filter(item => item.id == condition)[0]
    },
    // 查看
    // handleClick(type) {
    //   if (type === 1) {
    //     if (!this.valueAnalysis_pdf) return this.$message.error('暂无价值分析报告')
    //     this.pdfSrc = this.valueAnalysis_pdf + '?time=' + new Date().getMilliseconds()
    //   } else {
    //     if (!this.creditInformation_pdf) return this.$message.error('暂无征信分析报告')
    //     this.pdfSrc = this.creditInformation_pdf + '?time=' + new Date().getMilliseconds()
    //   }
    // },
    handleClick() {
      if (this.activeNameed === 'supplementaryInformation_valueAnalysis_AI') {
        if (!this.valueAnalysis_pdf) return this.$message.warning(`暂无${this.btnName}报告`)
        this.pdfSrc = this.valueAnalysis_pdf + '?time=' + new Date().getMilliseconds()
      } else if (this.activeNameed === 'supplementaryInformation_creditInformation_AI') {
        if (!this.creditInformation_pdf) return this.$message.warning(`暂无${this.btnName}报告`)
        this.pdfSrc = this.creditInformation_pdf + '?time=' + new Date().getMilliseconds()
      }
    },
    // 获取任务详情
    getDetail(taskId, processInsId) {
      this.getTaskDetail(taskId, processInsId).then(async res => {
        this.waiting = false

        const data = res.process
        if (data.processDefinitionKey === 'core_auto_apply_quota') {
          this.isCoreEnterprise = true
        }

        // 在 data 或 constants 中定义; AI接口调用流程
        const PROCESS_KEYS = [
          // 'apply_quota',
          'apply_quota__AI',
          // 'order_financing_apply_quota',
          'order_financing_apply_quota__AI',
          'order_financing_apply_quota__hkzh',
        ]
        // 在使用处
        const isKey = PROCESS_KEYS.includes(data.processDefinitionKey);

        // 应收账款：价值分析AI: 1; 征信分析AI: 2
        // 代采融资：价值分析AI: 3; 征信分析AI: 4
        // 订单融资：价值分析AI: 5; 征信分析AI: 6
        const businessTypeMap = {
          'apply_quota__AI': 1,
          'order_financing_apply_quota__AI': 5,
          'order_financing_apply_quota__hkzh': 5,
        }
        const businessType = businessTypeMap[data.processDefinitionKey] || 1

        if (isKey) {
          // PROCESS_KEYS 包含的流程
          this.activeNameed = 'supplementaryInformation_valueAnalysis_AI'
        } else {
          // 其他
          this.activeNameed = 'supplementaryInformation_valueAnalysis'
        }

        const { variables } = data
        const { taskForm } = res.form
        this.taskForm = taskForm
        this.resData = data
        this.variables = variables || {}
        this.variables.processInstanceId = res.process.processInstanceId
        const { processGoodsInfo } = variables
        this.processGoodsObj = processGoodsInfo

        // 放款信息显影
        const taskArrkey1 = [
          'applyProduct',
          'furtherInformationText',
          'furtherInformationAccessory',
          'supplementaryInformation',
          'contractSigning',
          'riskControlInformation',
        ]
        const taskFormFilter1 = taskForm.filter(item => taskArrkey1.includes(item.id) && item.readable)
        console.log('taskFormFilter1', taskFormFilter1)
        for (const item of taskFormFilter1) {
          this.modeReadable[item.id] = true
        }

        // 流程产品信息
        let loadTer,
          filterTabBar = []
        getDictionary('goods_load_term_unit').then(res => {
          const resData = res.data
          if (resData.code == 200) {
            // 处理字典数据
            const resList = []
            for (const item of resData.data) {
              resList.push({
                key: item.dictKey,
                value: item.dictValue,
                id: item.id,
              })
            }
            loadTer = resList.filter(
              // 过滤出当前的最长期限单位
              item => item.key == processGoodsInfo.loadTermUnit
            )
            getDictionary('goods_billing_method').then(res => {
              const resData = res.data
              if (resData.code == 200) {
                // 处理字典数据
                const resList2 = []
                for (const item of resData.data) {
                  resList2.push({
                    key: item.dictKey,
                    value: item.dictValue,
                    id: item.id,
                  })
                }
                if (processGoodsInfo.repaymentType === 1) {
                  if (processGoodsInfo.billingMethod) {
                    processGoodsInfo.billingMethod.split(',').forEach((item, index) => {
                      // 过滤出当前的计费方式
                      filterTabBar[index] = resList2.filter(itemed => itemed.key == item)[0].value
                    })
                  }
                } else {
                  filterTabBar = ['随借随还']
                }

                const filterTabBarStr = filterTabBar.toString()
                const data = [
                  {
                    id: 1,
                    label: '最高可借',
                    value: `${processGoodsInfo.loanAmountEnd}万元`,
                    key: 'applyProduct_loanAmountEnd',
                  },
                  // {
                  //   id: 2,
                  //   label: '年利率低至',
                  //   value: `${processGoodsInfo.annualInterestRateStart}%`,
                  //   key: 'applyProduct_annualInterestRate',
                  // },
                  {
                    id: 3,
                    label: '最长期限',
                    value: `${processGoodsInfo.loadTermEnd}${loadTer[0].value}`,
                    key: 'applyProduct_loadTermEnd',
                  },
                  // {
                  //   id: 4,
                  //   label: '计费方式',
                  //   value: filterTabBarStr,
                  //   key: 'applyProduct_billingMethod',
                  // },
                ]
                if (processGoodsInfo.annualInterestRateStart) {
                  const dataArr2 = {
                    id: 2,
                    label: '年利率低至',
                    value: `${processGoodsInfo.annualInterestRateStart}%`,
                    key: 'applyProduct_annualInterestRate',
                  }
                  data.splice(1, 0, dataArr2)
                }
                if (filterTabBarStr) {
                  const dataArr4 = {
                    id: 4,
                    label: '计费方式',
                    value: filterTabBarStr,
                    key: 'applyProduct_billingMethod',
                  }
                  data.splice(3, 0, dataArr4)
                }

                // 是否可读
                const dataKey = data.map(item => item.key)
                const taskFormFilter = taskForm.filter(item => dataKey.includes(item.id))
                const taskFormId = taskFormFilter.map(item => {
                  if (item.readable) {
                    return item.id
                  }
                })
                const dataFilter = data.filter(item => taskFormId.includes(item.key))
                this.tableData = dataFilter
              }
            })
          }
        })

        // 补充资料
        const { customerMaterial } = variables || {}
        this.customerMaterialList = JSON.parse(customerMaterial.creditForm)
        this.customerMaterialFormUpload = JSON.parse(customerMaterial.supplementMaterial).map(item => {
          item.uploadArr = item.uploadArr
            .map(itemed => {
              if (itemed.url) {
                const file = itemed.url.split('/')
                itemed.fileType = file[file.length - 1].split('.')[1]
                return itemed
              }
            })
            .filter(Boolean)
          return item
        })

        // 行方账户信息
        const accountName = variables.applyUserName
        const bankAccountInfoData = this.filterBox(taskForm, 'bankAccountInfo')
        this.bankAccountInfo = bankAccountInfoData && bankAccountInfoData.readable // 银行账户 显隐控制
        this.bankAccountInfoName = bankAccountInfoData && bankAccountInfoData.name
        if (this.bankAccountInfo) {
          let params = {
            goodsId: processGoodsInfo.id,
            userId: variables.userId,
          }
          // 行方账户信息
          this.getCustomerBankCardPerson(params).then((res) => {
            this.formControl = {
              accountNum: res.bankCardNo,
              accountName: accountName,
              depositBankCode: res.bankCode,
              depositBankName: res.bankDeposit,
            }
            const bankAccountInfoOptionArr = [
              {
                label: '账户',
                id: 'accountNum',
                type: 'input',
              },
              {
                label: '户名',
                id: 'accountName',
                type: 'input',
              },
              {
                label: '开户行行号',
                id: 'depositBankCode',
                type: 'input',
              },
              {
                label: '开户行行名',
                id: 'depositBankName',
                type: 'input',
              },
            ]
            const dataFilterArrbank = bankAccountInfoOptionArr.map(item => item.id)
            // 是否可读
            const arrbank = taskForm.filter(item => dataFilterArrbank.includes(item.id))
            const arredbank = arrbank.map(item => {
              if (item.readable) {
                return item.id
              }
            })
            const bankAccountInfoOptionFilterArr = bankAccountInfoOptionArr.filter(item => arredbank.includes(item.id))
            this.bankAccountInfoOption.column = bankAccountInfoOptionFilterArr.map(item => {
              // 优化后的代码
              return {
                label: item.label,
                prop: item.id,
                type: item.type,
                placeholder: `请输入${item.label}`,
                append: item.append,
                span: 12,
                display: true,
                // disabled: item.id === 'accountName' && accountName, // 只有accountName字段才禁用
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: `请输入${item.label}`,
                    trigger: 'blur',
                  },
                ],
              }
            })
            // 是否可写
            const arrDisabledbank = arrbank
              .map(item => {
                if (!item.writeable) {
                  return item.id
                }
              })
              .filter(Boolean)
            if (arrDisabledbank) {
              const arrDisabledId = this.bankAccountInfoOption.column.filter(item => arrDisabledbank.includes(item.prop))
              for (const item of arrDisabledId) {
                item.disabled = true
              }
            }
          })
        }

        // 补充信息
        // 回显补充信息
        const variablesObj = {
          ...variables.frontValue,
          ...variables.frontCreditInformation,
          ...variables.forntExtractResult,
          ...variables.forntExtractResult2
        }
        this.unsupplementaryInformationValue(variablesObj)
        // 控制补充信息tag栏
        const supplementaryInformationArr = this.supplementaryInformationData.map(item => item.name)

        const supplementaryInformationArrFilter = taskForm.filter(item => supplementaryInformationArr.includes(item.id))
        const arrs = supplementaryInformationArrFilter.map(item => {
          if (item.readable) {
            return {
              title: item.name,
              name: item.id,
            }
          }
        })
        this.supplementaryInformation = arrs.filter(Boolean)
        this.supplementaryInformationWriter = supplementaryInformationArrFilter
          .map(item => {
            if (item.writeable) {
              return item.id
            }
          })
          .filter(Boolean)
        // 价值分析Option
        const valueAnalysisOptionArr = [
          {
            label: '去年纳税销售总额',
            id: 'valueAnalysis_talTaxSalesLastYear',
            append: '万元',
          },
          {
            label: '前年纳税销售总额',
            id: 'valueAnalysis_talTaxSalesOfThePreviousYear',
            append: '万元',
          },
          {
            label: '净利润',
            id: 'valueAnalysis_retainedProfits',
            append: '万元',
          },
          {
            label: '去年应纳税总额',
            id: 'valueAnalysis_talAmountOfTaxPayableLastYear',
            append: '万元',
          },
          {
            label: '前年应纳税总额',
            id: 'valueAnalysis_talTaxableAmountOfPreviousYear',
            append: '万元',
          },
          {
            label: '同比销售增长率',
            id: 'valueAnalysis_yearSalesGrowthRate',
            append: '%',
          },
          {
            label: '环比销售增长率',
            id: 'valueAnalysis_sequentialSalesGrowthRate',
            append: '%',
          },
          {
            label: '应收账款总额',
            id: 'valueAnalysis_talAccountsReceivable',
            append: '万元',
          },
          {
            label: '应还账款总额',
            id: 'valueAnalysis_talAmountOfAccountsReceivable',
            append: '万元',
          },
          {
            label: '近1年回款率',
            id: 'valueAnalysis_recentYearReturnRate',
            append: '%',
          },
        ]
        const dataFilterArr = valueAnalysisOptionArr.map(item => item.id)
        // 是否可读
        const arr = taskForm.filter(item => dataFilterArr.includes(item.id))
        const arred = arr.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        const valueAnalysisOptionFilterArr = valueAnalysisOptionArr.filter(item => arred.includes(item.id))
        this.valueAnalysisOption.column = valueAnalysisOptionFilterArr.map(item => {
          return {
            label: item.label,
            prop: item.id,
            type: 'input',
            placeholder: `请输入${item.label}`,
            append: item.append,
            span: 12,
            display: true,
            disabled: false,
            rules: [
              {
                required: true,
                message: `请输入${item.label}`,
                trigger: 'blur',
              },
            ],
          }
        })
        // 是否可写
        const arrDisabled = arr
          .map(item => {
            if (!item.writeable) {
              return item.id
            }
          })
          .filter(Boolean)
        if (arrDisabled) {
          const arrDisabledId = this.valueAnalysisOption.column.filter(item => arrDisabled.includes(item.prop))
          for (const item of arrDisabledId) {
            item.disabled = true
          }
        }

        // 允许AI接口调用的流程
        if (isKey) {
          // 价值分析AI 获取值赋值
          if (!variables.forntExtractResult) {
            const extractResult = await this.getExtractResult(this.variables.processInstanceId, businessType).catch(error => error)
            if (extractResult) {

              const moneyFields = [
                'cash_flow',
                'Monetary_funds',
                'Taxes_payable',
                'Other_receivables',
                'Other_payables',
                'Total_liabilities',
                'Total_assets',
                'Total_equity',
                'Admin_expenses',
                'Finance_Charges',
                'Total_profit',
                'Income_tax_expenses',
                'talTaxSalesLastYear',
                'talTaxSalesOfThePreviousYear',
                'retainedProfits',
                'talAccountsReceivable',
                'talAmountOfAccountsReceivable',
                'Current_Assets',
                'Current_liabilities',
                'NO_Current_liabilities',
                'Inventory',
                'Operating_costs',
              ];
              const formattedFields = {};

              moneyFields.forEach(field => {
                formattedFields[field] = formatMoney2(extractResult[field], 2);
              });


              this.formControl = {
                ...extractResult,
                ...formattedFields,
                valueAnalysis_pdf: extractResult.pdfUrl
              }

              this.valueAnalysis_pdf = this.formControl.valueAnalysis_pdf
            }
          }
          // 征信信息AI 获取值赋值
          if (!variables.forntExtractResult2) {
            const extractResult2 = await this.getExtractResult2(this.variables.processInstanceId, businessType).catch(error => error)
            if (extractResult2) {
              this.formControl = {
                ...extractResult2,
                creditInformation_pdf: extractResult2.pdfUrl
              }

              this.creditInformation_pdf = this.formControl.creditInformation_pdf
            }
          }
        }
        // 价值分析AI Option
        const valueAnalysisOptionArrAI = [
          {
            label: '经营活动现金流量',
            id: 'cash_flow',
            append: '元',
          },
          {
            label: '货币资金',
            id: 'Monetary_funds',
            append: '元',
          },
          {
            label: '应交税费',
            id: 'Taxes_payable',
            append: '元',
          },
          {
            label: '其他应收款',
            id: 'Other_receivables',
            append: '元',
          },
          {
            label: '其他应付款',
            id: 'Other_payables',
            append: '元',
          },
          {
            label: '负债合计',
            id: 'Total_liabilities',
            append: '元',
          },
          {
            label: '资产总计',
            id: 'Total_assets',
            append: '元',
          },
          {
            label: '所有者权益合计',
            id: 'Total_equity',
            append: '元',
          },
          {
            label: '管理费用',
            id: 'Admin_expenses',
            append: '元',
          },
          {
            label: '财务费用',
            id: 'Finance_Charges',
            append: '元',
          },
          {
            label: '利润总额',
            id: 'Total_profit',
            append: '元',
          },
          {
            label: '所得税费用',
            id: 'Income_tax_expenses',
            append: '元',
          },
          {
            label: '去年营业收入',
            id: 'talTaxSalesLastYear',
            append: '元',
          },
          {
            label: '前年营业收入',
            id: 'talTaxSalesOfThePreviousYear',
            append: '元',
          },
          {
            label: '净利润',
            id: 'retainedProfits',
            append: '元',
          },
          {
            label: '应还账款总额',
            id: 'talAccountsReceivable',
            append: '元',
          },
          {
            label: '应收账款总额',
            id: 'talAmountOfAccountsReceivable',
            append: '元',
          },
          {
            label: '流动资产',
            id: 'Current_Assets',
            append: '元',
          },
          {
            label: '流动负债',
            id: 'Current_liabilities',
            append: '元',
          },
          {
            label: '非流动负债',
            id: 'NO_Current_liabilities',
            append: '元',
          },
          {
            label: '存货',
            id: 'Inventory',
            append: '元',
          },
          {
            label: '营业成本',
            id: 'Operating_costs',
            append: '元',
          },
          {
            label: '毛利率',
            id: 'Gross_margin',
            append: '%',
          },
          {
            label: '流动比率',
            id: 'Current_Ratio',
            append: '%',
          },
          {
            label: '速动比率',
            id: 'Quick_Ratio',
            append: '%',
          },
          {
            label: '现金比率',
            id: 'Cash_Ratio',
            append: '%',
          },
          {
            label: '资产负债率',
            id: 'Debt_Ratio',
            append: '%',
          },
          {
            label: '净资产收益率',
            id: 'ROE',
            append: '%',
          },
          {
            label: '销售净利率',
            id: 'Net_Profit_Margin',
            append: '%',
          },
          {
            label: '经营现金流覆盖率',
            id: 'Operating_Cash_Flow_Ratio',
            append: '%',
          },
          {
            label: '经营现金流/负债比',
            id: 'OCF_to_Debt_Ratio',
            append: '%',
          },
          {
            label: '应收账款周转率',
            id: 'Receivables_Turnover',
            append: '%',
          },
          {
            label: '存货周转率',
            id: 'Inventory_Turnover',
            append: '%',
          },
          {
            label: '',
            id: 'valueAnalysis_pdf',
            append: '',
            type: 'slot'
          },
        ]
        const dataFilterArrAI = valueAnalysisOptionArrAI.map(item => item.id)
        // 是否可读
        const arrAI = taskForm.filter(item => dataFilterArrAI.includes(item.id))
        const arredAI = arrAI.map(item => {
          if (item.readable) {
            return item.id
          }
        })

        // 查看报告价值分析AI
        if (arredAI.includes('valueAnalysis_pdf')) {
          this.isValueAnalysis_pdf_btn = true
        } else {
          this.isValueAnalysis_pdf_btn = false
        }

        const valueAnalysisOptionFilterArrAI = valueAnalysisOptionArrAI.filter(item => arredAI.includes(item.id))
        this.valueAnalysisOptionAI.column = valueAnalysisOptionFilterArrAI.map(item => {
          return {
            label: item.label,
            prop: item.id,
            type: item.type === 'slot' ? 'slot' : 'input',
            // type: 'input',
            placeholder: `请输入${item.label}`,
            append: item.append,
            span: 12,
            display: true,
            disabled: item.type === 'slot' ? false : true,
            rules: [
              {
                required: item.type === 'slot' ? false : true,
                message: `请输入${item.label}`,
                trigger: 'blur',
              },
            ],
          }
        })
        // 是否可写
        const arrDisabledAI = arrAI
          .map(item => {
            if (!item.writeable) {
              return item.id
            }
          })
          .filter(Boolean)
        if (arrDisabledAI) {
          const arrDisabledIdAI = this.valueAnalysisOptionAI.column.filter(item => arrDisabledAI.includes(item.prop))
          for (const item of arrDisabledIdAI) {
            item.disabled = true
          }
        }
        // 征信信息Option
        const creditInformationOption = [
          {
            label: '当前关注',
            id: 'creditInformation_currentFocusOn',
            append: '笔',
          },
          {
            label: '当前次级',
            id: 'creditInformation_currentSubprime',
            append: '笔',
          },
          {
            label: '当前可疑',
            id: 'creditInformation_currentSuspicious',
            append: '笔',
          },
          {
            label: '当前损失',
            id: 'creditInformation_currentLoss',
            append: '笔',
          },
          {
            label: '当前逾期',
            id: 'creditInformation_currentOverdue',
            append: '笔',
          },
          {
            label: '近24个月逾期为M1',
            id: 'creditInformation_M1',
            append: '笔',
          },
          {
            label: '近24个月逾期为M2',
            id: 'creditInformation_M2',
            append: '笔',
          },
          {
            label: '近24个月逾期为M3',
            id: 'creditInformation_M3',
            append: '笔',
          },
          {
            label: '近2个月审批、征信查询',
            id: 'creditInformation_nearly2MonthsForApproval',
            append: '笔',
          },
          {
            label: '近12个月审批、征信查询',
            id: 'creditInformation_nearly12MonthsForApproval',
            append: '笔',
          },
          {
            label: '企业涉诉案件',
            id: 'creditInformation_casesInvolvingEnterprisesInLitigation',
            append: '笔',
          },
          {
            label: '企业涉诉未结案件',
            id: 'creditInformation_unresolvedCasesInvolvingLitigationOfEnterprises',
            append: '次',
          },
          {
            label: '逾期率',
            id: 'creditInformation_yuqilv',
            append: '%',
          },
        ]
        const dataFilterArr1 = creditInformationOption.map(item => item.id)
        // 是否可读
        const arr1 = taskForm.filter(item => dataFilterArr1.includes(item.id))
        const arred1 = arr1.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        const creditInformationOptionFilterArr = creditInformationOption.filter(item => arred1.includes(item.id))
        this.creditInformationOption.column = creditInformationOptionFilterArr.map(item => {
          return {
            label: item.label,
            prop: item.id,
            type: 'input',
            placeholder: `请输入${item.label}`,
            append: item.append,
            span: 12,
            display: true,
            disabled: false,
            rules: [
              {
                required: true,
                message: `请输入${item.label}`,
                trigger: 'blur',
              },
            ],
          }
        })
        // 是否可写
        const arrDisabled1 = arr1
          .map(item => {
            if (!item.writeable) {
              return item.id
            }
          })
          .filter(Boolean)
        if (arrDisabled1) {
          const arrDisabledId = this.creditInformationOption.column.filter(item => arrDisabled1.includes(item.prop))
          for (const item of arrDisabledId) {
            item.disabled = true
          }
        }
        // 征信信息OptionAI
        const creditInformationOptionAI = [
          {
            label: '当前关注',
            id: 'currentFocusOn',
            append: '笔',
          },
          {
            label: '当前次级',
            id: 'currentSubprime',
            append: '笔',
          },
          {
            label: '当前可疑',
            id: 'currentSuspicious',
            append: '笔',
          },
          {
            label: '当前损失',
            id: 'currentLoss',
            append: '笔',
          },
          {
            label: '当前逾期',
            id: 'currentOverdue',
            append: '笔',
          },
          {
            label: '近24个月逾期为M1',
            id: 'M1',
            append: '笔',
          },
          {
            label: '近24个月逾期为M2',
            id: 'M2',
            append: '笔',
          },
          {
            label: '近24个月逾期为M3',
            id: 'M3',
            append: '笔',
          },
          {
            label: '近2个月审批、征信查询',
            id: 'nearly2MonthsForApproval',
            append: '笔',
          },
          {
            label: '近12个月审批、征信查询',
            id: 'nearly12MonthsForApproval',
            append: '笔',
          },
          {
            label: '企业涉诉案件',
            id: 'casesInvolvingEnterprisesInLitigation',
            append: '笔',
          },
          {
            label: '企业涉诉未结案件',
            id: 'unresolvedCasesInvolvingLitigationOfEnterprises',
            append: '次',
          },
          {
            label: '逾期率',
            id: 'yuqilv',
            append: '%',
          },
          {
            label: '',
            id: 'creditInformation_pdf',
            append: '',
            type: 'slot',
          },
        ]
        const dataFilterArr1AI = creditInformationOptionAI.map(item => item.id)
        // 是否可读
        const arr1AI = taskForm.filter(item => dataFilterArr1AI.includes(item.id))
        const arred1AI = arr1AI.map(item => {
          if (item.readable) {
            return item.id
          }
        })

        // 查看报告征信分析AI
        if (arred1AI.includes('creditInformation_pdf')) {
          this.isCreditInformation_pdf_btn = true
        } else {
          this.isCreditInformation_pdf_btn = false
        }

        const creditInformationOptionFilterArrAI = creditInformationOptionAI.filter(item => arred1AI.includes(item.id))
        this.creditInformationOptionAI.column = creditInformationOptionFilterArrAI.map(item => {
          return {
            label: item.label,
            prop: item.id,
            type: item.type === 'slot' ? 'slot' : 'input',
            // type: 'input',
            placeholder: `请输入${item.label}`,
            append: item.append,
            span: 12,
            display: true,
            disabled: true,
            rules: [
              {
                required: item.type === 'slot' ? false : true,
                message: `请输入${item.label}`,
                trigger: 'blur',
              },
            ],
          }
        })

        // 合同签署list
        const cotractNameList = [
          'contractSigning_serialNumber',
          'contractSigning_contractNumber',
          'contractSigning_contractTitle',
          'contractSigning_creationTime',
          'contractSigning_signTheState',
          'contractSigning_operation',
        ]
        const cotractNameListFilter = taskForm.filter(item => cotractNameList.includes(item.id))
        const readableList = cotractNameListFilter.map(item => {
          if (item.readable) {
            return item.id
          }
        })
        for (const key in this.tableData2Readable) {
          readableList.forEach(item => {
            if (item == key) {
              this.tableData2Readable[key] = true
            }
          })
        }
        const paramsed = {
          ids: variables.contractId,
        }
        if (!variables.contractId) return
        getByContractId(paramsed).then(res => {
          const resData = res.data
          if (resData.code == 200) {
            let num = 1
            for (const item of resData.data) {
              let statusText = ''
              if (item.status) {
                switch (item.status) {
                  case 1:
                    statusText = '待签署'
                    break
                  case 2:
                    statusText = '已取消'
                    break
                  case 3:
                    statusText = '已签署'
                    break
                  case 4:
                    statusText = '已失效'
                    break
                  case 5:
                    statusText = '已完成'
                    break
                  case 6:
                    statusText = '签署中'
                    break
                }
              }
              this.tableData2.push({
                id: item.id,
                serial: String(num),
                contractTitle: item.contractTitle,
                contractId: item.contractId,
                createTime: item.createTime,
                statusText: statusText,
              })
              num++
            }
          }
        })
      })
    },
    forValidata(pass) {
      const arrData = this.supplementaryInformationData.filter(item =>
        this.supplementaryInformationWriter.includes(item.name)
      )
      if (arrData.length) {
        arrData.forEach((item, index) => {
          if (item.refed && index + 1 == arrData.length) {
            this.validata(pass, item, 'last')
          } else if (item.refed) {
            this.validata(pass, item)
          }
        })
      } else {
        this.supplementaryInformationValue()
        if (this.modeReadable.riskControlInformation && pass) {
          this.$refs.riskControlForm.forValidata(pass)
        } else {
          this.validataFunction(pass)
        }
      }
    },
    async getCustomerBankCardPerson(params) {
      const { data: resData } = await customerBankCardPerson(params)
      if (resData.success) {
        return resData.data
      }
    },
    // 封装校验方法
    validata(pass, refed, last) {
      // 通过前校验
      const thi = `this.$refs.${refed.refed}[0]`
      eval(thi).validate((valid, done, msg) => {
        if (!valid) {
          let errMsg = Object.values(msg)[0][0].message
          if (!errMsg) {
            errMsg = '必填项未填'
          }
          if (!this.lock) {
            this.lock = true
            this.$message.error(errMsg)
            setTimeout(() => {
              this.lock = false
            }, 500)
          }
          this.supplementaryInformation = this.supplementaryInformation.map((item, index) => {
            if (item.name == refed.name && index != 0) {
              item.errorM = true
            }
            return item
          })
        } else if (last) {
          this.supplementaryInformationValue()
          if (this.modeReadable.riskControlInformation && pass) {
            this.$refs.riskControlForm.forValidata(pass)
          } else {
            this.validataFunction(pass)
          }
        }
        done()
      })
    },
    // 通过
    handleExamine(pass) {
      if (pass) {
        this.forValidata(pass)
      } else {
        this.validataFunction(pass)
      }
    },
    // 通过后调取接口函数
    validataFunction(pass) {
      this.submitLoading = true
      this.handleCompleteTask(pass, this.objData)
        .then(() => {
          this.$message.success('处理成功')
          this.$router.$avueRouter.closeTag()
          this.handleCloseTag('/business/businessprocess')
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    // 补充信息处理
    supplementaryInformationValue() {
      const value = this.formControl
      const supplementaryInformationObj = {
        frontValue: {
          totalTaxLastYear: value.valueAnalysis_talTaxSalesLastYear,
          totalTaxPreviousYear: value.valueAnalysis_talTaxSalesOfThePreviousYear,
          netProfit: value.valueAnalysis_retainedProfits,
          totalTaxPayableLastYear: value.valueAnalysis_talAmountOfTaxPayableLastYear,
          totalTaxPayablePreviousYear: value.valueAnalysis_talTaxableAmountOfPreviousYear,
          yearGrowth: value.valueAnalysis_yearSalesGrowthRate,
          annulusGrowth: value.valueAnalysis_sequentialSalesGrowthRate,
          totalAccountsReceivable: value.valueAnalysis_talAccountsReceivable,
          totalStill: value.valueAnalysis_talAmountOfAccountsReceivable,
          collectionRate: value.valueAnalysis_recentYearReturnRate,
        },
        frontCreditInformation: {
          currentConcerns: value.creditInformation_currentFocusOn,
          currentTimes: value.creditInformation_currentSubprime,
          currentSuspicious: value.creditInformation_currentSuspicious,
          currentLoss: value.creditInformation_currentLoss,
          currentOverdue: value.creditInformation_currentOverdue,
          overdueMone: value.creditInformation_M1,
          overdueMtwo: value.creditInformation_M2,
          overdueMthree: value.creditInformation_M3,
          approveLetterQueryTwo: value.creditInformation_nearly2MonthsForApproval,
          approveLetterQueryTetracosa: value.creditInformation_nearly12MonthsForApproval,
          closedCases: value.creditInformation_casesInvolvingEnterprisesInLitigation,
          openCases: value.creditInformation_unresolvedCasesInvolvingLitigationOfEnterprises,
          overdueRate: value.creditInformation_yuqilv,
        },
      }
      this.objData = supplementaryInformationObj
    },
    // 补充信息反处理
    unsupplementaryInformationValue(value) {
      const unsupplementaryInformationObj = {
        valueAnalysis_talTaxSalesLastYear: value.totalTaxLastYear,
        valueAnalysis_talTaxSalesOfThePreviousYear: value.totalTaxPreviousYear,
        valueAnalysis_retainedProfits: value.netProfit,
        valueAnalysis_talAmountOfTaxPayableLastYear: value.totalTaxPayableLastYear,
        valueAnalysis_talTaxableAmountOfPreviousYear: value.totalTaxPayablePreviousYear,
        valueAnalysis_yearSalesGrowthRate: value.yearGrowth,
        valueAnalysis_sequentialSalesGrowthRate: value.annulusGrowth,
        valueAnalysis_talAccountsReceivable: value.totalAccountsReceivable,
        valueAnalysis_talAmountOfAccountsReceivable: value.totalStill,
        valueAnalysis_recentYearReturnRate: value.collectionRate,
        //
        creditInformation_currentFocusOn: value.currentConcerns,
        creditInformation_currentSubprime: value.currentTimes,
        creditInformation_currentSuspicious: value.currentSuspicious,
        creditInformation_currentLoss: value.currentLoss,
        creditInformation_currentOverdue: value.currentOverdue,
        creditInformation_M1: value.overdueMone,
        creditInformation_M2: value.overdueMtwo,
        creditInformation_M3: value.overdueMthree,
        creditInformation_nearly2MonthsForApproval: value.approveLetterQueryTwo,
        creditInformation_nearly12MonthsForApproval: value.approveLetterQueryTetracosa,
        creditInformation_casesInvolvingEnterprisesInLitigation: value.closedCases,
        creditInformation_unresolvedCasesInvolvingLitigationOfEnterprises: value.openCases,
        creditInformation_yuqilv: value.overdueRate,
        // 价值分析AI
        cash_flow: formatMoney2(value.cash_flow),
        Monetary_funds: formatMoney2(value.Monetary_funds),
        Taxes_payable: formatMoney2(value.Taxes_payable),
        Other_receivables: formatMoney2(value.Other_receivables),
        Other_payables: formatMoney2(value.Other_payables),
        Total_liabilities: formatMoney2(value.Total_liabilities),
        Total_assets: formatMoney2(value.Total_assets),
        Total_equity: formatMoney2(value.Total_equity),
        Admin_expenses: formatMoney2(value.Admin_expenses),
        Finance_Charges: formatMoney2(value.Finance_Charges),
        Total_profit: formatMoney2(value.Total_profit),
        Income_tax_expenses: formatMoney2(value.Income_tax_expenses),
        talTaxSalesLastYear: formatMoney2(value.talTaxSalesLastYear),
        talTaxSalesOfThePreviousYear: formatMoney2(value.talTaxSalesOfThePreviousYear),
        retainedProfits: formatMoney2(value.retainedProfits),
        talAccountsReceivable: formatMoney2(value.talAccountsReceivable),
        talAmountOfAccountsReceivable: formatMoney2(value.talAmountOfAccountsReceivable),
        Current_Assets: formatMoney2(value.Current_Assets),
        Current_liabilities: formatMoney2(value.Current_liabilities),
        NO_Current_liabilities: formatMoney2(value.NO_Current_liabilities),
        Inventory: formatMoney2(value.Inventory),
        Operating_costs: formatMoney2(value.Operating_costs),
        Gross_margin: formatMoney2(value.Gross_margin),
        Interest_Coverage: formatMoney2(value.Interest_Coverage),
        ROE: value.ROE,
        Cash_Ratio:value.Cash_Ratio,
        Debt_Ratio: value.Debt_Ratio,
        Quick_Ratio:value.Quick_Ratio,
        Current_Ratio:value.Current_Ratio,
        Net_Profit_Margin: value.Net_Profit_Margin,
        OCF_to_Debt_Ratio:value.OCF_to_Debt_Ratio,
        Inventory_Turnover:value.Inventory_Turnover,
        Receivables_Turnover: value.Receivables_Turnover,
        Operating_Cash_Flow_Ratio: value.Operating_Cash_Flow_Ratio,
        valueAnalysis_pdf: value.valueAnalysis_pdf,
        // 征信信息AI
        currentFocusOn: value.currentFocusOn,
        currentSubprime: value.currentSubprime,
        currentSuspicious: value.currentSuspicious,
        currentLoss: value.currentLoss,
        currentOverdue: value.currentOverdue,
        M1: value.M1,
        M2: value.M2,
        M3: value.M3,
        nearly2MonthsForApproval: value.nearly2MonthsForApproval,
        nearly12MonthsForApproval: value.nearly12MonthsForApproval,
        casesInvolvingEnterprisesInLitigation: value.casesInvolvingEnterprisesInLitigation,
        unresolvedCasesInvolvingLitigationOfEnterprises: value.unresolvedCasesInvolvingLitigationOfEnterprises,
        yuqilv: value.yuqilv,
        creditInformation_pdf: value.creditInformation_pdf,
      }

      // 征信pdf
      this.valueAnalysis_pdf = value.valueAnalysis_pdf
      // 征信pdf
      this.creditInformation_pdf = value.creditInformation_pdf

      this.formControl = unsupplementaryInformationObj
    },
    // 子传父函数
    childByValue(value) {
      const obj = {
        riskControlAdvice: {
          lineOfCredit: value.lineOfCredit,
          riskThat: value.riskThat,
        },
        managementCredit: {
          chooseEnterprise: value.chooseEnterprise,
          selectTheManagement: value.selectTheManagement,
          bondProportion: value.bondProportion1,
          quotaType: value.lineType1,
          recycleType: value.recycleType1,
          // effectiveTime: value.effectiveDate1,
          expireTime: value.dueDate1,
          loanable: value.loanableInto1,
        },
        finalApproveAmount: {
          finalAmount: value.finalBatchOfTheForehead,
          serviceRate: value.serviceTariffing,
          quotaType: value.lineType2,
          recycleType: value.recycleType2,
          // effectiveTime: value.effectiveDate2,
          expireTime: value.dueDate2,
          annualInterestRate: value.financingCostRate,
          loanable: value.loanableInto2,
          bondProportion: value.bondProportion2,
          financingProportion: value.financingProportion,
        },
        score: value.score,
      }
      this.objData = { ...this.objData, ...obj }
    },
    filterBox(arr, condition) {
      return arr.filter(item => item.id == condition)[0]
    },
    handleChange() {
      // 企业信息-财务信息折叠面板收缩控制
      this.changeType = !this.changeType
    },
    handleChange3() {
      // 补充资料折叠面板收缩控制
      this.change3Type = !this.change3Type
    },
    // 预览合同
    viewContract(item) {
      const params = {
        contractId: item.row.contractId,
      }
      skipToPreview(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    // 下载合同
    downContract(item) {
      const params = {
        contractId: item.row.contractId,
      }
      contractDownload(params).then(res => {
        const resData = res.data
        if (resData.code == 200) {
          window.open(resData.data)
        }
      })
    },
    viewGoods() {
      goodsTypeToPath(this.processGoodsObj)
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding: 0 10px 10px 0;
  padding: 0 20px 10px;
  margin: 0 -20px;
}

.apply-container {
  display: flex;
  flex-wrap: nowrap;

  .left,
  .right {
    width: 100%;
    height: 157px;
    padding: 20px;
    line-height: 20px;
    border-radius: 8px;
    background-color: rgba(246, 246, 246, 100);
    text-align: center;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-right: 12px;

    .form-item {
      width: 100%;
    }

    .title {
      display: inline-block;
      width: 130px;
      text-align: right;
      color: rgba(125, 125, 125, 100);
      font-size: 14px;
    }

    .value {
      display: inline-block;
      width: calc(100% - 130px);
      text-align: left;
      color: rgba(16, 16, 16, 100);
      font-size: 14px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: 12px;

    > * {
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .right-icon-box {
      display: flex;
      flex-direction: column;
    }

    .status {
      color: rgba(16, 16, 16, 100);
      font-size: 16px;
      text-align: center;
      font-family: SourceHanSansSC-bold;
      font-weight: blod;
    }

    .desc {
      color: rgba(132, 134, 141, 100);
      font-size: 14px;
      text-align: center;
      font-family: SourceHanSansSC-regular;
    }
  }
}

.info-container {
  .form {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 12px;
    border: 1px solid #e0e3ea;

    > div {
      width: 135px;
      height: 48px;
      padding: 0 6px;
      flex-shrink: 0;
      line-height: 48px;
      background-color: rgba(247, 247, 247, 100);
      color: #000;
      font-size: 14px;
      text-align: left;
      border-right: 1px solid #e0e3ea;

      &.value {
        flex-grow: 1;
        width: 230px;
        background-color: #fff;
      }

      &:last-child {
        border-right: none;
      }
    }
  }
}

.approval-container {
  display: flex;
  flex-wrap: nowrap;

  .title {
    flex-shrink: 0;
    // text-align: right;
  }

  .value {
    flex: 1;
  }
}

.custom-button {
  ::v-deep {
    .foot-item {
      width: calc(100% - 280px) !important;
      margin-left: 8px !important;
    }
  }
}

.creditLimitFinancing {
  margin-bottom: 40px;
}

.fromHeader {
  width: 100%;
  font-size: 16px;
  font-family: SourceHanSansSC-bold;
  background: #fff;
  color: rgba(16, 16, 16, 100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fromLeft {
  display: flex;
  align-items: center;
  width: 60%;

  .fromLeft-title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
    font-family: SourceHanSansSC-regular;
  }
}

.i-active {
  transform: rotate(0deg) !important;
}

.boxs {
  .boxs-to-apply-for-product {
    box-sizing: border-box;

    h1 {
      margin: 0;
    }

    .boxs-to-apply-for-product-h1 {
      height: 22px;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      text-align: left;
      font-family: SourceHanSansSC-regular;
    }
    .boxs-to-apply-for-product-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .boxs-to-apply-for-product-left-logo {
        .boxs-to-apply-for-product-left-logo-box {
          margin-top: 17px;
          display: flex;
          justify-content: center;
          align-items: center;

          .boxs-to-apply-for-product-left-logo-box-img {
            width: 48px;
            height: 48px;
            border-radius: 40px;
            border: 1px solid rgba(234, 234, 234, 100);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            margin-right: 7px;

            & > img {
              width: 100%;
              object-fit: cover;
            }
          }
          .boxs-to-apply-for-product-left-logo-box-goodname {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-direction: column;

            & > span:first-child {
              // width: 168px;
              height: 24px;
              color: rgba(18, 119, 255, 100);
              font-size: 16px;
              text-align: left;
              font-family: SourceHanSansSC-bold;
              text-decoration: underline;
              font-weight: 600;
              cursor: pointer;
            }
            & > span:last-child {
              // width: 168px;
              height: 18px;
              color: rgba(105, 124, 255, 100);
              font-size: 14px;
              text-align: left;
              font-family: SourceHanSansSC-regular;
            }
          }
        }
      }
      .boxs-to-apply-for-product-right-goodtype {
        // width: 107px;
        // height: 24px;
        border-radius: 24px;
        background-color: rgba(18, 119, 255, 100);
        color: rgba(255, 255, 255, 100);
        font-size: 14px;
        text-align: center;
        line-height: 24px;
        font-family: Microsoft Yahei;
        padding: 2px 10px;
        box-sizing: border-box;
      }
    }
    .boxs-to-apply-for-product-body {
      margin-top: 20px;

      ::v-deep {
        .has-gutter {
          .el-table__cell {
            background-color: #fff1f1;

            .cell {
              font-size: 14px;
              color: #000;
            }
          }
        }
        .el-table__body tr:hover > td.el-table__cell {
          background-color: #fff;
        }
      }

      .view {
        color: #3894ff;
        margin-right: 10px;
        cursor: pointer;
      }
      .down {
        color: #3894ff;
        cursor: pointer;
      }
    }
  }
}

.descriptions-for-box {
  margin-top: 15px;
  position: relative;
  z-index: 1;
  ::v-deep {
    .el-descriptions-item__label.is-bordered-label {
      width: 13.5%;
      height: 48px;
      line-height: 20px;
      background-color: rgba(247, 247, 247, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
    .el-descriptions-item__content {
      width: 360px;
      height: 48px;
      line-height: 20px;
      background-color: rgba(255, 255, 255, 100);
      color: rgba(255, 255, 255, 100);
      font-size: 14px;
      text-align: left;
      color: #000;
      padding-left: 15px;
    }
  }
}

// 更改外部组件默认样式
::v-deep i.el-icon-caret-bottom {
  font-size: 150%;
  transform: rotate(-90deg);
  transition: transform 0.4s;
}
::v-deep i.el-collapse-item__arrow {
  display: none;
}
::v-deep div.el-collapse-item__header {
  height: 15px;
  border-bottom: none;
}
::v-deep div.el-collapse-item__content {
  padding-bottom: 0 !important;
}
::v-deep .el-collapse {
  border-top: none;
  border-bottom: none;
}
::v-deep .el-card {
  border-radius: 8px;
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
.bord-radius-left {
  border-radius: 20px 0 0 20px !important;
  border-left-color: #b3d8ff !important;
}
.bord-radius-right {
  border-radius: 0 20px 20px 0 !important;
  border-right-color: #b3d8ff !important;
}
// 覆盖组件库样式
::v-deep {
  .el-tabs--card {
    .el-tabs__header {
      border-bottom: none;
    }
    .el-tabs__item {
      border: none;
      padding: 0;
    }
    .el-tabs__item:nth-child(2) {
      padding-left: 0 !important;
    }
    .is-plain {
      background: #fff;
      border-color: #b3d8ff;
    }
    .is-plain:hover {
      color: #409eff;
      background: #ecf5ff;
      border-color: #b3d8ff;
    }
  }
  .avue-form__menu {
    display: none;
  }
  .el-input.is-disabled .el-input__inner {
    color: #000;
  }
  .el-textarea.is-disabled .el-textarea__inner {
    color: #000;
  }
  span {
    display: inline-block;
  }
}
</style>
