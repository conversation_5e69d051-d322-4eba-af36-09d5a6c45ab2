<template>
  <basic-container>
    <avue-crud
      :option='option'
      :table-loading='loading'
      :data='data'
      :page.sync='page'
      :permission='permissionList'
      :before-open='beforeOpen'
      v-model='form'
      ref='crud'
      @row-update='rowUpdate'
      @row-save='rowSave'
      @row-del='rowDel'
      @search-change='searchChange'
      @search-reset='searchReset'
      @selection-change='selectionChange'
      @current-change='currentChange'
      @size-change='sizeChange'
      @refresh-change='refreshChange'
      @on-load='onLoad'
    >
      <template slot='menuLeft'>
        <el-button
          type='primary'
          size='small'
          icon='el-icon-plus'
          @click="handleClickDetail(_, 'Add')"
        >新增
        </el-button>
      </template>

      <template slot-scope='{ row }' slot='status'>
        <el-tag
          :style="{
            color: row.status == 1 ? '#67c23a' : '#A6AEBC',
            border: row.status == 1 ? '1px solid #67c23a' : '1px solid #C9CED6',
            background: row.status == 1 ? '#EAFCF7' : '#fff',
          }"
        >{{ row.status == 1 ? '已启用' : '已禁用' }}
        </el-tag>
      </template>

      <!--    费用类型颜色加深-->
<!--      <template slot-scope='{ row }' slot='expenseType'>-->
<!--        <el-tag-->
<!--          :style="{-->
<!--            color: 'rgb(16, 16, 16)',-->
<!--            border: '1px solid rgba(234, 236, 241, 100)',-->
<!--            backgroundColor: 'rgba(234, 236, 241, 100)',-->
<!--            borderRadius: '17px',-->
<!--            height: '28px',-->
<!--            lineHeight: '28px',-->
<!--          }"-->
<!--        >-->
<!--          {{ row.expenseTypeStr }}-->
<!--        </el-tag>-->
<!--      </template>-->

      <!-- 计算节点颜色加深-->
<!--      <template slot-scope='{ row, dic }' slot='calculationNode'>-->
<!--        <el-tag-->
<!--          :style="{-->
<!--      color: 'rgb(16, 16, 16)',-->
<!--      border: '1px solid rgba(234, 236, 241, 100)',-->
<!--      backgroundColor: 'rgba(234, 236, 241, 100)',-->
<!--      borderRadius: '17px',-->
<!--      height: '28px',-->
<!--      lineHeight: '28px',-->
<!--      }"-->
<!--        >-->
<!--          {{ dic[row.collectFeesNode]}}-->
<!--        </el-tag>-->
<!--      </template>-->
<!--          收费节点颜色加深-->
<!--      <template slot-scope='{ row, dic }' slot='collectFeesNode'>-->
<!--        <el-tag-->
<!--          :style="{-->
<!--            color: 'rgb(16, 16, 16)',-->
<!--            border: '1px solid rgba(234, 236, 241, 100)',-->
<!--            backgroundColor: 'rgba(234, 236, 241, 100)',-->
<!--            borderRadius: '17px',-->
<!--            height: '28px',-->
<!--            lineHeight: '28px',-->
<!--          }"-->
<!--        >-->
<!--          {{ dic[row.collectFeesNode - 1].dictValue }}-->
<!--        </el-tag>-->
<!--      </template>-->

      <template slot-scope='{ row, index, type, size }' slot='menu'>
        <el-button
          icon='el-icon-view'
          :size='size'
          :type='type'
          @click.stop="handleClickDetail(row, 'Get')"
        >查看
        </el-button
        >
        <el-button
          icon='el-icon-edit'
          v-if='row.status === 0'
          :size='size'
          :type='type'
          @click.stop="handleClickDetail(row, 'Edit')"
        >编辑
        </el-button
        >
        <el-button
          icon='el-icon-del'
          v-if='row.status === 0'
          :size='size'
          :type='type'
          @click.stop='$refs.crud.rowDel(row, index)'
        >删除
        </el-button
        >
        <el-button
          type='text'
          icon='el-icon-success'
          size='small'
          v-if='row.status == 1'
          @click.stop='updateStatus(row)'
        >禁用
        </el-button>

        <el-button
          type='text'
          icon='el-icon-warning'
          size='small'
          v-if='row.status == 0'
          @click.stop='updateStatus(row)'
        >启用
        </el-button
        >
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { add, getDetail, getList, remove, update, updateStatusById } from '@/api/goods/goodsexpenserule'
import { mapGetters } from 'vuex'

var DIC = {
  STATUS: [
    {
      label: '已禁用',
      value: 0,
    },
    {
      label: '已启用',
      value: 1,
    },
  ],
}
export default {
  data() {
    return {
      dics: {},
      dicsfeeNode: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        align: 'center',
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        dialogClickModal: false,
        column: [
          {
            label: '费用名称',
            prop: 'name',
            search: true,
          },
          {
            label: '费用类型',
            prop: 'expenseTypeStr',
            // dataType: 'str',
            // dicUrl:
            //   '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_type',
            // props: {
            //   label: 'dictValue',
            //   value: 'dictKey',
            // },
            slot: true,
          },
          {
            label: '费用类型',
            prop: 'expenseType',
            search: true,
            showColumn: false,
            hide: true,
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_type',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            search: true,
            type: 'select',
          },
          {
            label: '计算节点',
            prop: 'feeNode',
            dataType:'string',
            dicUrl:
              '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_fee_node',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '收费节点',
            prop: 'collectFeesNode',
            dataType:'string',
            dicUrl:
            '/api/blade-system/dict-biz/dictionary?code=goods_expense_rule_fee_node',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
          },
          {
            label: '计费公式',
            prop: 'feeFormulaName',
          },
          {
            label: '上一次修改时间',
            prop: 'updateTime',
          },
          {
            label: '操作人',
            prop: 'userName',
          },
          {
            label: '状态',
            prop: 'status',
            dicData: DIC.SEX,
            mock: {
              type: 'dic',
            },
          },
        ],
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.goodsexpenserule_add, false),
        viewBtn: this.vaildData(this.permission.goodsexpenserule_view, false),
        delBtn: this.vaildData(this.permission.goodsexpenserule_delete, false),
        editBtn: this.vaildData(this.permission.goodsexpenserule_edit, false),
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    updateStatus(row) {
      // 如果是禁用状态，就需要修改为启用状态
      let msg = row.status === 0 ? '确认要启用?' : '确认进行禁用'
      this.$confirm(msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let status = row.status === 0 ? 1 : 0
          let id = row.id
          return updateStatusById(id, status)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    // 新增、查看、编辑
    handleClickDetail(row, btnName) {
      const params = {
        id: row ? row.id : '',
        btnName,
      }
      this.$router.push(
        '/expense/costRulesDetails/' +
        Buffer.from(JSON.stringify(params)).toString('base64'),
      )
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          window.console.log(error)
        },
      )
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          done()
        },
        error => {
          loading()
          console.log(error)
        },
      )
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.$refs.crud.toggleSelection()
        })
    },
    beforeOpen(done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data
        })
      }
      done()
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query),
      ).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>
