<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      v-model="form"
      ref="crud"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @sort-change="sortChange"
      @on-load="onLoad"
    >
      <template slot-scope="{ row, type, size }" slot="menuLeft">
        <el-button icon="el-icon-plus" :size="size" type="primary" @click="handleAdd">新增</el-button>
      </template>
      <template slot-scope="{ row, type, size }" slot="menu">
        <el-button :size="size" :type="type" @click="handleDetail(row)">详情</el-button>
        <el-button :size="size" :type="type" @click="handleEdit(row)">编辑</el-button>
        <el-button :size="size" :type="type" @click="handleH5Link(row)">H5链接</el-button>
        <el-button :size="size" :type="type" @click="handleUpload(row)">资方补充资料</el-button>
        <el-button :size="size" :type="type" @click="handleDelete(row)">删除</el-button>
      </template>
    </avue-crud>

    <!-- 平台端居间发起咨询 -->
    <el-dialog
      :title="isEdit ? '编辑' : '新增' + '咨询资料'"
      :visible.sync="dialogAdd"
      width="60%"
      :close-on-click-modal="false"
      top="5vh"
      append-to-body
      custom-class="dialog-add"
      @close="closeAdd"
    >
      <div class="dialog-add-content">
        <!-- 基本信息 -->
        <div class="dialog-add-content-box">
          <div class="dialog-add-title" @click="toggleCollapse('1')">
            <span>基本资料</span>
            <i v-if="!activeNames.includes('1')" class="el-icon-caret-right"></i>
            <i v-else class="el-icon-caret-bottom"></i>
          </div>
          <div :class="['collapse-content', { 'is-active': activeNames.includes('1') }]">
            <!-- 折叠内容 -->
            <el-form :model="formInfo" :rules="formInfoRules" ref="formInfoRef" label-width="100px">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="咨询用户" prop="consultUserName">
                    <el-input v-model="formInfo.consultUserName" placeholder="请输入咨询用户"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="联系方式" prop="phone">
                    <el-input v-model="formInfo.phone" placeholder="请输入联系方式"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="融资产品" prop="goodsId">
                    <el-select
                      v-model="formInfo.goodsId"
                      placeholder="请选择融资产品"
                      style="width: 100%"
                      filterable
                      clearable
                      @change="goodsChange"
                    >
                      <el-option
                        v-for="item in intermediationProductList"
                        :key="item.id"
                        :label="item.goodsName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="logo">
                    <el-upload
                      class="avatar-uploader"
                      action="/api/blade-resource/oss/endpoint/put-file-kv"
                      :show-file-list="false"
                      :on-success="handleAvatarSuccess"
                      :before-upload="beforeAvatarUpload"
                    >
                      <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <div v-if="imageUrl" class="avatar-delete" @click="deleteAvatar">
                      <i class="el-icon-delete"></i> 删除
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="水印" prop="isShowWatermark">
                    <el-switch v-model="formInfo.isShowWatermark" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="12">
                  <el-form-item label="业务类型" prop="consultBusinessType">
                    <el-select
                      v-model="formInfo.consultBusinessType"
                      placeholder="请选择业务类型"
                      style="width: 100%"
                      :disabled="!formInfo.goodsId"
                    >
                      <el-option
                        v-for="item in consultBusinessTypeList"
                        :key="item.dictKey"
                        :label="item.dictValue"
                        :value="item.dictValue"
                      />
                    </el-select>
                  </el-form-item>
                </el-col> -->
              </el-row>
            </el-form>
          </div>
        </div>
        <!-- 授信表单 -->
        <div class="dialog-add-content-box">
          <div class="dialog-add-title" @click="toggleCollapse('2')">
            <span>授信资料</span>
            <i v-if="!activeNames.includes('2')" class="el-icon-caret-right"></i>
            <i v-else class="el-icon-caret-bottom"></i>
          </div>
          <div :class="['collapse-content', { 'is-active': activeNames.includes('2') }]">
            <!-- 折叠内容 -->
            <el-form
              v-if="templateList.length"
              :model="formCredit"
              :rules="formCreditRules"
              ref="formCreditRef"
              label-width="auto"
            >
              <el-row :gutter="20" v-for="item in templateList" :key="item.id">
                <el-col :span="24">
                  <span style="font-size: 14px; color: #333; font-weight: bold">{{ item.templateName }}</span>
                </el-col>
                <template v-for="subItem in item.creditFromFields">
                  <el-col :span="subItem.dataType === 8 ? 24 : 12" :key="subItem.id">
                    <el-form-item :label="subItem.fieldDesc" :prop="subItem.fieldName">
                      <el-input
                        v-if="subItem.dataType === 1"
                        v-model="formCredit[subItem.fieldName]"
                        :placeholder="`请输入${subItem.fieldDesc}`"
                        type="number"
                        :min="0"
                      ></el-input>
                      <!-- <el-select
                      v-if="subItem.dataType === 2"
                      v-model="formCredit[subItem.fieldName]"
                      :placeholder="`请输入${subItem.fieldDesc}`"
                      style="width: 100%"
                      filterable
                      clearable
                    >
                      <el-option
                        v-for="item in getOptionsList(subItem.fieldName)"
                        :key="item.dictKey"
                        :label="item.dictValue"
                        :value="item.dictValue"
                      />
                    </el-select> -->
                      <el-cascader
                        v-else-if="subItem.dataType === 2"
                        :placeholder="`请选择${subItem.fieldDesc}`"
                        v-model="formCredit[subItem.fieldName]"
                        :options="getOptionsList(subItem.fieldName, 'cascader')"
                        style="width: 100%"
                        clearable
                        filterable
                        :show-all-levels="false"
                        @change="handleCascaderChange"
                      ></el-cascader>
                      <el-input
                        v-else-if="subItem.dataType === 3"
                        v-model="formCredit[subItem.fieldName]"
                        :placeholder="`请输入${subItem.fieldDesc}`"
                      >
                      </el-input>
                      <el-input
                        v-else-if="subItem.dataType === 8"
                        v-model="formCredit[subItem.fieldName]"
                        :placeholder="`请输入${subItem.fieldDesc}`"
                        type="textarea"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                      >
                      </el-input>
                      <el-input
                        v-else-if="subItem.dataType === 4"
                        v-model="formCredit[subItem.fieldName]"
                        :placeholder="`请输入${subItem.fieldDesc}`"
                      >
                      </el-input>
                      <el-input
                        v-else-if="subItem.dataType === 6"
                        v-model="formCredit[subItem.fieldName]"
                        :placeholder="`请输入${subItem.fieldDesc}`"
                        type="number"
                        :min="0"
                      >
                      </el-input>
                      <el-select
                        v-else-if="subItem.dataType === 9"
                        v-model="formCredit[subItem.fieldName]"
                        :placeholder="`请选择${subItem.fieldDesc}`"
                        style="width: 100%"
                        multiple
                        filterable
                        clearable
                        @change="handleSelectChange"
                      >
                        <el-option
                          v-for="item in getOptionsList(subItem.fieldName, 'select')"
                          :key="item.dictKey"
                          :label="item.dictValue"
                          :value="item.dictValue"
                        />
                      </el-select>
                      <template v-else-if="subItem.dataType === 7">
                        <el-select
                          v-model="formCredit[subItem.fieldName]"
                          :placeholder="`请选择${subItem.fieldDesc}`"
                          style="width: 100%; margin-bottom: 20px"
                          multiple
                          filterable
                          @change="handleSelectChange"
                        >
                          <el-option
                            v-for="item in getOptionsList(subItem.fieldName, 'select')"
                            :key="item.dictKey"
                            :label="item.dictValue"
                            :value="item.dictValue"
                          />
                        </el-select>
                      </template>
                    </el-form-item>
                  </el-col>
                  <!-- 说明 -->
                  <template v-if="subItem.dataType === 7">
                    <el-col
                      :span="24"
                      class="risk_measures_card"
                      v-if="Array.isArray(formCredit[subItem.fieldName]) && formCredit[subItem.fieldName].length"
                    >
                      <h4 style="margin-left: 20px; margin-bottom: 20px">{{ subItem.fieldDesc }}说明</h4>
                      <el-col :span="24" v-for="(sub, idx) in formCredit[subItem.fieldName]" :key="idx">
                        <el-form-item :label="sub">
                          <el-input
                            v-model="formCredit[`${subItem.fieldName}_${sub}`]"
                            :placeholder="`请输入${sub}说明`"
                            type="textarea"
                            :autosize="{ minRows: 4, maxRows: 8 }"
                          />
                        </el-form-item>
                      </el-col>
                    </el-col>
                  </template>
                </template>
              </el-row>
            </el-form>
            <el-empty v-else description="请选择融资产品" :image-size="50" />
          </div>
        </div>
        <!-- 补充资料 -->
        <div class="dialog-add-content-box">
          <div class="dialog-add-title" @click="toggleCollapse('3')">
            <span>补充资料</span>
            <i v-if="!activeNames.includes('3')" class="el-icon-caret-right"></i>
            <i v-else class="el-icon-caret-bottom"></i>
          </div>
          <div :class="['collapse-content', { 'is-active': activeNames.includes('3') }]">
            <!-- 折叠内容 -->
            <el-form
              v-if="goodsMaterialList.length"
              :model="formMaterial"
              :rules="formMaterialRules"
              ref="formMaterialRef"
              label-width="auto"
            >
              <el-row :gutter="20">
                <el-col :span="24" v-for="(item, index) in goodsMaterialList">
                  <el-form-item :label="item.materialName">
                    <el-upload
                      class="upload-demo"
                      :ref="'upload_' + index"
                      drag
                      action="/api/blade-resource/oss/endpoint/put-file-kv"
                      multiple
                      :on-remove="(file, fileList) => handleRemove(file, fileList, index)"
                      :file-list="item.fileList"
                      :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, index)"
                      :disabled="disabledView"
                      :on-preview="file => handlePreview(file, index)"
                      accept=".png, .jpg"
                    >
                      <i class="el-icon-upload"></i>
                      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                      <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过5M</div>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <el-empty v-else description="请选择融资产品" :image-size="50" />
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeAdd">取消</el-button>
        <el-button type="primary" @click="submitAdd" :loading="loading">确认</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="补充资料"
      :visible.sync="dialogVisible"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      top="5vh"
      @close="closeDialog"
      append-to-body
    >
      <el-form ref="form" :model="formData" label-width="80px" :rules="rules">
        <el-form-item label="资料描述" prop="materialDesc">
          <el-input type="textarea" v-model="formData.materialDesc" placeholder="请输入资料描述" :rows="5" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">确认</el-button>
      </span>
    </el-dialog>

    <!-- H5 地址链接 -->
    <el-dialog title="H5 分享二维码" :visible.sync="dialogVisibleH5" width="300px" append-to-body>
      <!-- <div class="h5-url">
        <span class="url" @click="url">{{ operator }}</span>
        <span class="copy" @click="copy">复制链接</span>
      </div> -->
      <div class="qrcode-container">
        <vue-qrcode :value="operator" :options="{ width: 200 }"></vue-qrcode>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  addCapitalSupplementMaterial,
  getCreditFormTemplateListApi,
  getGoodsMaterialListApi,
  submitConsultApi,
  canOperatorApi,
  getDictionaryApi,
  getIntermediationProductApi,
  getDetailApi,
  deleteApi,
} from '@/api/Intermediation/index'
import { mapGetters } from 'vuex'
import VueQrcode from '@chenfengyuan/vue-qrcode'

export default {
  components: {
    VueQrcode,
  },
  data() {
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入手机号'))
      } else if (!/^1[3456789]\d{9}$/.test(value)) {
        callback(new Error('手机号格式不正确，请输入11位有效手机号'))
      } else {
        callback()
      }
    }
    return {
      form: {},
      operator: false,
      loading: true,
      query: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        dialogClickModal: false,
        menuWidth: 300,
        column: [
          {
            label: '咨询用户',
            prop: 'consultUserName',
            search: true,
            searchLabelWidth: 100,
          },
          {
            label: '手机号',
            prop: 'phone',
          },
          {
            label: '产品名称',
            prop: 'goodsName',
          },
          {
            label: '咨询时间',
            prop: 'date',
            search: true,
            type: 'daterange',
            dataType: 'array',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            searchRange: true,
            hide: true,
            showColumn: false,
          },
          {
            label: '咨询时间',
            prop: 'createTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
          },
        ],
      },
      data: [],
      capitalList: [],

      dialogVisible: false,
      formData: {
        materialDesc: '',
        intermediationConsultId: '',
      },
      rules: {
        materialDesc: [{ required: true, message: '请输入资料描述', trigger: 'blur' }],
      },

      // 咨询资料
      dialogAdd: false,
      activeNames: ['1', '2', '3'],
      formInfo: {
        consultUserName: '',
        phone: '',
        consultBusinessType: '',
        goodsId: '',
        logo: '',
        isShowWatermark: 0,
      },
      formInfoRules: {
        consultUserName: [{ required: true, message: '请输入咨询人姓名', trigger: 'blur' }],
        phone: [{ required: true, validator: validatePass, trigger: 'blur' }],
        goodsId: [{ required: true, message: '请选择融资产品', trigger: 'change' }],
        consultBusinessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
      },
      templateList: [],
      goodsMaterialList: [],
      formCredit: {},
      formMaterial: {},

      dialogVisibleH5: false,

      // 字典值
      // fundTypeList: [],
      // consultBusinessTypeList: [],
      // tradeList: [],
      // riskMeasuresList: [],
      // 产品列表
      intermediationProductList: [],
      isEdit: false,
      editId: '',
      imageUrl: '',
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {}
    },
    ids() {
      let ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  methods: {
    async handleDelete(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return deleteApi(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
        })
    },
    async handleEdit(row) {
      this.isEdit = true
      this.dialogAdd = true
      this.editId = row.id
      await this.getIntermediationProduct()
      // 触发产品选择变化，加载授信表单和补充资料模板
      await this.goodsChange(row.goodsId)

      const { data: res } = await getDetailApi(row.id)
      const { customerMaterial, data } = res.data

      this.formInfo = {
        consultUserName: res.data.consultUserName,
        phone: res.data.phone,
        consultBusinessType: res.data.consultBusinessType,
        goodsId: res.data.goodsId,
        isShowWatermark: res.data.isShowWatermark,
        logo: res.data.logo,
      }
      this.imageUrl = res.data.logo

      // 授信资料回显
      if (customerMaterial && customerMaterial.data) {
        try {
          const savedData = JSON.parse(customerMaterial.data)
          this.formCredit = { ...savedData }
        } catch (e) {
          console.error('解析授信资料失败:', e)
        }
      }

      // 补充资料回显
      if (customerMaterial.supplementMaterial) {
        try {
          const supplementMaterial = JSON.parse(customerMaterial.supplementMaterial)
          // 将回显的文件数据映射到对应的材料列表中
          this.goodsMaterialList = this.goodsMaterialList.map(item => {
            const material = supplementMaterial.find(m => m.id === item.id)
            if (material && material.uploadArr && Array.isArray(material.uploadArr)) {
              return {
                ...item,
                fileList: material.uploadArr.map(file => ({
                  name: file.originalName || file.name || file.fileName,
                  url: file.link || file.url || file.filePath,
                  ...file,
                })),
              }
            }

            return {
              ...item,
              fileList: [],
            }
          })
        } catch (e) {
          console.error('解析补充资料失败:', e)
        }
      }
    },
    handleSelectChange(val) {
      // console.log(val, '---112')
    },
    url() {
      if (this.operator) {
        window.open(this.operator)
      }
    },
    copy() {
      console.log(this.operator, '复制')
      // 检查浏览器是否支持 Clipboard API
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(this.operator)
          .then(() => {
            this.$message.success('复制成功')
          })
          .catch(err => {
            console.error('复制失败:', err)
            this.$message.error('复制失败，请重试')
          })
      } else {
        // 旧版浏览器的兼容方案
        const textarea = document.createElement('textarea')
        textarea.value = this.operator
        document.body.appendChild(textarea)
        textarea.select()
        try {
          document.execCommand('copy')
          this.$message.success('复制成功')
        } catch (err) {
          console.error('复制失败:', err)
          this.$message.error('复制失败，请重试')
        } finally {
          document.body.removeChild(textarea)
        }
      }
    },
    handleH5Link(row) {
      canOperatorApi('intermediation_consult_h5_url').then(res => {
        console.log(res.data.data, '-res.data.data')
        let url = res.data.data.paramValue
        this.operator = url + '?id=' + row.id

        if (this.operator) {
          this.dialogVisibleH5 = true
        }
      })
    },
    handleCascaderChange() {},

    // // 修改 getOptionsList 方法
    // getOptionsList(fieldName) {
    //   // 尝试根据字段名动态查找对应的数据列表
    //   const listName = `${this.toCamelCase(fieldName)}List`

    //   if (this.hasOwnProperty(listName) && Array.isArray(this[listName])) {
    //     return this[listName]
    //   }

    //   // 如果找不到对应的数据列表，返回空数组
    //   return this[listName] || []
    // },
    // 修改 getOptionsList 方法以兼容多种选择器组件
    getOptionsList(fieldName, ConType) {
      // 下拉框
      if (ConType === 'select') {
        const listName = `${this.toCamelCase(fieldName)}List`

        if (this.hasOwnProperty(listName) && Array.isArray(this[listName])) {
          return this[listName]
        }

        // 如果找不到对应的数据列表，返回空数组
        return this[listName] || []
      }

      // 级联
      // 尝试根据字段名动态查找对应的数据列表
      const listName = `${this.toCamelCase(fieldName)}List`

      // 检查数据是否存在
      if (this.hasOwnProperty(listName) && Array.isArray(this[listName])) {
        const dataList = this[listName]

        // 处理空数组情况
        if (dataList.length === 0) {
          return []
        }

        // 检查是否为字典数据格式（包含 dictKey）
        if (dataList[0].hasOwnProperty('dictKey')) {
          return dataList.map(item => ({
            value: item.dictValue, // 传文字
            label: item.dictValue,
            // 处理级联数据
            children: item.children ? this.transformDictToCascader(item.children) : undefined,
          }))
        }

        // 检查是否已经是 cascader 格式
        if (dataList[0].hasOwnProperty('value') && dataList[0].hasOwnProperty('label')) {
          return dataList
        }

        // 其他情况，返回原始数据
        return dataList
      }

      // 如果找不到对应的数据列表，返回空数组
      return []
    },
    // 辅助方法：将字典数据转换为级联格式
    transformDictToCascader(dictList) {
      if (!Array.isArray(dictList) || dictList.length === 0) {
        return []
      }

      return dictList.map(item => ({
        value: item.dictValue,
        label: item.dictValue, // 传文字
        children: item.children ? this.transformDictToCascader(item.children) : undefined,
      }))
    },

    // 动态提取需要的字典键
    getRequiredDictionaryKeys() {
      const keys = new Set()

      // 从 templateList 中提取 dataType 为 2 或 7 的字段名
      this.templateList.forEach(template => {
        if (template.creditFromFields) {
          template.creditFromFields.forEach(field => {
            if (field.dataType === 2 || field.dataType === 7 || field.dataType === 9) {
              keys.add(field.fieldName)
            }
          })
        }
      })

      // 添加固定的字典键
      keys.add('consult_business_type')

      return Array.from(keys)
    },

    // 修改 getDictionary 方法
    async getDictionary(keys) {
      // 如果传入的是数组，则批量获取
      if (Array.isArray(keys)) {
        const promises = keys.map(key => getDictionaryApi(key))
        try {
          const results = await Promise.allSettled(promises)
          results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
              const key = keys[index]
              this.setDictionaryData(key, result.value.data.data)
            } else {
              console.error(`获取字典 ${keys[index]} 失败:`, result.reason)
            }
          })
        } catch (error) {
          console.error('批量获取字典数据异常:', error)
        }
      } else {
        // 保持原有单个获取的逻辑
        try {
          const { data } = await getDictionaryApi(keys)
          this.setDictionaryData(keys, data.data)
        } catch (error) {
          console.error(`获取字典 ${keys} 失败:`, error)
        }
      }
    },
    // 新增设置字典数据的方法
    setDictionaryData(key, data) {
      // 使用统一的命名规则：将下划线命名转换为驼峰命名 + List
      const listName = `${this.toCamelCase(key)}List`

      // if (this.hasOwnProperty(listName)) {
      //   this[listName] = data
      // }
      this[listName] = data
    },
    // 下划线转驼峰命名
    toCamelCase(str) {
      return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase())
    },

    handlePreview(file, index) {
      console.log(file, '--file')
      // 使用正则表达式进行更严谨的文件类型判断
      const fileName = file.url || ''
      const isPdfWord = /\.(pdf|doc|docx)$/i.test(fileName)
      if (isPdfWord) {
        this.$message.warning('该文件类型暂不支持预览')
        return
      }

      const imgSrcArr = []
      imgSrcArr.push({ url: file.url })
      this.$ImagePreview(imgSrcArr, 0, {
        closeOnClickModal: true,
      })
    },
    handleSuccess(response, file, fileList, index) {
      console.log(response, '--response')
      if (response && response.data.url) {
        // 修正：使用正确的索引来更新文件列表
        if (this.goodsMaterialList[index]) {
          if (!this.goodsMaterialList[index].fileList) {
            this.goodsMaterialList[index].fileList = []
          }
          this.goodsMaterialList[index].fileList.push({
            url: response.data.url,
            name: file.name,
            // 补充一些信息
            attachId: response.data.attachId,
            link: response.data.url,
            originalName: file.name,
          })
        }
      }
    },
    handleRemove(file, fileList, index) {
      console.log(file, fileList)
      // 修正：使用正确的索引来更新文件列表
      if (this.goodsMaterialList[index]) {
        this.goodsMaterialList[index].fileList = fileList
      }
    },

    // 头像logo
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw)
      this.formInfo.logo = res.data.url
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    deleteAvatar() {
      this.imageUrl = ''
      // 如果需要清除表单中的相关字段，也可以在这里处理
      // 例如: this.formInfo.logo = ''
      this.formInfo.logo = ''
    },

    toggleCollapse(name) {
      const index = this.activeNames.indexOf(name)
      if (index > -1) {
        this.activeNames.splice(index, 1)
      } else {
        this.activeNames.push(name)
      }
    },
    // 新增
    async handleAdd() {
      this.isEdit = false
      this.dialogAdd = true
      let goodsId = '1960529224868163585'
      // await this.getCreditForm(goodsId)
      // await this.getGoodsMaterial(goodsId)
      await this.getIntermediationProduct()
    },
    closeAdd() {
      this.isEdit = false
      this.dialogAdd = false
      this.$refs.formInfoRef.resetFields()
      this.formCredit = {}
      this.formMaterial = {}
      this.templateList = []
      this.goodsMaterialList = []
    },
    // 运营端录入
    submitAdd() {
      this.$refs.formInfoRef.validate(async valid => {
        if (valid) {
          // 补充资料
          const supplementMaterial = this.goodsMaterialList.map(item => {
            return {
              ...item,
              uploadArr: item.fileList,
            }
          })
          // 授信表单
          const creditForm = this.templateList.map(item => {
            if (!item.creditFromFields) return item

            const updatedFields = item.creditFromFields.map(subItem => ({
              ...subItem,
              value: this.formCredit[subItem.fieldName] || '',
            }))

            return {
              ...item,
              creditFromFields: updatedFields,
            }
          })

          console.log(supplementMaterial, '==supplementMaterial')

          let params = {
            ...this.formInfo,
            data: JSON.stringify(this.formCredit), // 授信表单json值
            supplementMaterial: JSON.stringify(supplementMaterial),
            creditForm: JSON.stringify(creditForm),
            // goodsId: '1960529224868163585',
          }

          if (this.isEdit) {
            params.id = this.editId
          }

          const { data: resData } = await submitConsultApi(params)
          if (resData.success) {
            this.$message.success('提交成功')
            this.onLoad(this.page)
            this.dialogAdd = false
            this.closeAdd()
          }
        }
      })
    },
    // 授信表单
    async getCreditForm(goodsId) {
      const param = {
        goodsId,
        goodsType: 99,
        enterpriseType: 1,
      }
      const {
        data: { data: list },
      } = await getCreditFormTemplateListApi(param)

      // 排序处理
      this.templateList = list.map(({ creditFromFields, ...item }) => ({
        ...item,
        // creditFromFields: creditFromFields.sort((a, b) => a.orderNum - b.orderNum),
        creditFromFields: creditFromFields.sort((a, b) => {
          // dataType === 8 的排在最后
          if (a.dataType === 8 && b.dataType !== 8) {
            return 1 // a 排在 b 后面
          }
          if (a.dataType !== 8 && b.dataType === 8) {
            return -1 // a 排在 b 前面
          }
          // dataType 相同的情况下，按 orderNum 排序
          return a.orderNum - b.orderNum
        }),
      }))

      // 获取需要的字典键并加载对应数据
      const requiredKeys = this.getRequiredDictionaryKeys()
      await this.getDictionary(requiredKeys)
    },
    async goodsChange(val) {
      if (val) {
        await this.getCreditForm(val)
        await this.getGoodsMaterial(val)
      } else {
        this.templateList = []
        this.goodsMaterialList = []
      }
    },
    async getGoodsMaterial(goodsId) {
      const params = {
        goodsId,
        uploadNode: '2-0',
        uploadUser: 1,
      }
      const {
        data: { data: list },
      } = await getGoodsMaterialListApi(params)
      this.goodsMaterialList = list.map(item => ({
        ...item,
        fileList: [],
      }))
    },
    // 融资产品
    async getIntermediationProduct() {
      let params = {}
      const { data } = await getIntermediationProductApi(params)
      if (data.success) {
        this.intermediationProductList = data.data
      }
    },

    // 资方补充资料
    submit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.loading = true
          let params = { ...this.formData }
          try {
            const { data } = await addCapitalSupplementMaterial(params)
            if (data.success) {
              this.$message({
                message: '操作成功',
                type: 'success',
              })
              this.closeDialog()
              this.onLoad(this.page)
            }
          } catch (error) {
            this.loading = false
          }
        }
      })
    },
    async handleUpload(row) {
      this.dialogVisible = true
      this.formData.intermediationConsultId = row.id
    },
    closeDialog() {
      this.$refs.form.resetFields()
      this.dialogVisible = false
      this.formData = {
        content: '',
      }
    },
    handleDetail(row) {
      this.$router.push({
        path: '/Intermediation/detail',
        query: {
          id: row.id,
        },
      })
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params

      if (params.date) {
        this.query.createDateStart = params.date[0]
        this.query.createDateEnd = params.date[1]
        delete this.query.date
      }

      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-add {
  height: 80vh !important;
  .dialog-add-content {
    height: 65vh;
    overflow-y: auto;

    .dialog-add-content-box {
      .dialog-add-title {
        height: 22px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        text-align: left;
        font-family: SourceHanSansSC-regular;
        font-weight: 700;
        cursor: pointer;
        margin-bottom: 20px;
      }

      .collapse-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s ease-out;
      }

      .collapse-content.is-active {
        max-height: 10000px; /* 设置一个足够大的值 */
        transition: max-height 0.5s ease-in;
      }
    }
  }
}

.h5-url {
  .url {
    font-size: 16px;
    color: #007fff;
    margin-right: 40px;
    cursor: pointer;
    text-decoration: underline;
  }
  .copy {
    font-size: 16px;
    color: #007fff;
    padding: 10px;
    background: #dbe5f1;
    border-radius: 15px;
    cursor: pointer;
  }
}

.risk_measures_card {
  // background: #000;
  // display: block;
  background: #f5f5f5;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  padding-top: 20px;
  border-radius: 20px;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.qrcode-container {
  text-align: center;
  margin-top: 20px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #8c939d;
  width: 48px;
  height: 48px;
  line-height: 48px;
  text-align: center;
}
.avatar {
  width: 48px;
  height: 48px;
  display: block;
}
.avatar-delete {
  color: #ff4d4f;
  font-size: 12px;
  cursor: pointer;
  text-align: left;
  margin-top: 5px;
}

.avatar-delete:hover {
  color: #ff7875;
}
</style>
