<template>
  <basic-container>
    <avue-affix id="avue-view" :offset-top="114">
      <div class="header">
        <avue-title :value="'融资信息详情'"></avue-title>
      </div>
    </avue-affix>
    <!-- 咨询 -->
    <basic-container>
      <el-descriptions title="" :column="3" border>
        <el-descriptions-item label="咨询人" :labelStyle="{ width: '200px' }">
          {{ detailData.consultUserName }}
        </el-descriptions-item>
        <el-descriptions-item label="融资产品">
          {{ detailData.goodsName }}
        </el-descriptions-item>
        <el-descriptions-item label="咨询时间">
          {{ detailData.createTime }}
        </el-descriptions-item>
      </el-descriptions>
    </basic-container>
    <!-- 补充资料文本 -->
    <basic-container v-for="(item, index) in customerMaterialList" :key="index">
      <!-- 展示表单 -->
      <div class="title" style="margin-bottom: 20px">{{ item.templateName }}</div>
      <div class="descriptions-for-box">
        <el-descriptions title="" :column="1" border>
          <el-descriptions-item
            v-for="(itemed, indexed) in item.creditFromFields"
            :key="indexed"
            :label="itemed.fieldDesc"
            :labelStyle="{ width: '200px' }"
          >
            <span v-if="[5, 6].includes(itemed.dataType)">
              {{ itemed.value }}
              <template v-if="itemed.value">
                {{ itemed.dataType === 5 ? '元' : '万元' }}
              </template>
            </span>
            <span v-else-if="[2].includes(itemed.dataType)">
              {{ (itemed.value && itemed.value[itemed.value.length - 1]) || itemed.value }}
            </span>
            <!-- 兜底措施 -->
            <span v-else-if="[7].includes(itemed.dataType)">
              <!-- {{ itemed.value ? itemed.value.join(',') : '' }} -->
              <template v-for="(subItem, idx) in itemed.value">
                <div :class="{ 'descriptions-for-box-title-border': idx != 0, 'descriptions-for-box-title': true }">
                  {{ subItem }}:
                </div>
                <div style="text-indent: 2em; line-height: 1.5; margin-bottom: 10px">
                  {{ creditFormData[`${itemed.fieldName}_${subItem}`] }}
                </div>
              </template>
            </span>
            <span v-else-if="[9].includes(itemed.dataType)">
              {{ itemed.value ? itemed.value.join(',') : '' }}
            </span>
            <span v-else>
              {{ itemed.timeValue || itemed.value || '' }}
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </basic-container>
    <!-- 补充资料附件 -->
    <basic-container>
      <div class="title" style="margin-bottom: 20px">补充资料</div>
      <!-- 展示表单 -->
      <FilePreviewHWP v-if="customerMaterialFormUpload.length" :formUpload="customerMaterialFormUpload" />
      <el-empty v-else description="暂无补充资料" :image-size="50"></el-empty>
    </basic-container>
    <!-- 已上传 -->
    <basic-container>
      <div class="title" style="margin-bottom: 20px">已上传资方资料</div>
      <FilePreviewHWP
        isFileName
        v-if="capitalSupplementMaterialsList.length"
        :formUpload="capitalSupplementMaterialsList"
        :isFileTypeNum="false"
      />
      <el-empty v-else description="暂无已上传资料" :image-size="50"></el-empty>
    </basic-container>
    <!-- 待上传 -->
    <basic-container>
      <div class="title" style="margin-bottom: 20px">待上传资方资料</div>
      <FilePreviewHWP
        isFileName
        v-if="capitalSupplementMaterialsUploadList.length"
        :formUpload="capitalSupplementMaterialsUploadList"
        :isFileTypeNum="false"
        :isUpload="true"
        @handleUpload="handleUpload"
      />
      <el-empty v-else description="暂无待上传资料" :image-size="50"></el-empty>
    </basic-container>
    <!-- 底部按钮 -->
    <basic-container style="margin-bottom: 20px">
      <div class="menu-box">
        <span class="go-back" @click="goBack()">返回</span>
      </div>
    </basic-container>

    <el-dialog
      title="咨询资料"
      :visible.sync="dialogVisible"
      width="50%"
      :close-on-click-modal="false"
      top="5vh"
      append-to-body
      custom-class="dialog-add"
      @close="closeUpload"
    >
      <el-form :model="formMaterial" :rules="formMaterialRules" ref="formMaterialRef" label-width="auto">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="goodsMaterialData.materialName">
              <el-upload
                class="upload-demo"
                ref="upload_ref"
                drag
                action="/api/blade-resource/oss/endpoint/put-file-kv"
                multiple
                :on-remove="(file, fileList) => handleRemove(file, fileList)"
                :file-list="goodsMaterialData.fileList"
                :on-success="(response, file, fileList) => handleSuccess(response, file, fileList)"
                :on-preview="file => handlePreview(file)"
                accept=".png, .jpg"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过5M</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUpload">取消</el-button>
        <el-button type="primary" @click="submitUpload" :loading="loading">确认</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>
<script>
import FilePreviewHWP from './component/preview-documents/index.vue'
import { getDetailApi, submitMaterialApi } from '@/api/Intermediation/index'

export default {
  components: {
    FilePreviewHWP,
  },
  data() {
    return {
      customerMaterialFormUpload: [],
      customerMaterialList: [],
      creditFormData: {},
      detailData: {},
      // 已上传
      capitalSupplementMaterialsList: [],
      // 待上传
      capitalSupplementMaterialsUploadList: [],

      dialogVisible: false,
      goodsMaterialData: {},
      formMaterial: {},
      loading: false,
    }
  },

  methods: {
    async submitUpload() {
      // this.loading = true
      console.log(this.goodsMaterialData, '--this.goodsMaterialData')
      let materialFileJson = this.goodsMaterialData.fileList.map(item => {
        return {
          fileName: item.name,
          fileUrl: item.url,
        }
      })
      let params = [
        {
          id: this.goodsMaterialData.id,
          materialFileJson: JSON.stringify(materialFileJson),
        },
      ]

      const { data } = await submitMaterialApi(params)

      if (data.success) {
        this.$message.success('上传成功')
        this.getDetailData()
        this.closeUpload()
      }
    },
    closeUpload() {
      this.dialogVisible = false
      this.formMaterial = {}
      this.goodsMaterialData = {}
    },
    handlePreview(file, index) {
      console.log(file, '--file')
      const fileName = file.url || ''
      const isPdfWord = /\.(pdf|doc|docx)$/i.test(fileName)
      if (isPdfWord) {
        this.$message.warning('该文件类型暂不支持预览')
        return
      }

      const imgSrcArr = []
      imgSrcArr.push({ url: file.url })
      this.$ImagePreview(imgSrcArr, 0, {
        closeOnClickModal: true,
      })
    },
    handleSuccess(response, file, fileList) {
      console.log(response, '--response')
      if (response && response.data.url) {
        // 修正：使用正确的索引来更新文件列表
        if (this.goodsMaterialData) {
          if (!this.goodsMaterialData.fileList) {
            this.goodsMaterialData.fileList = []
          }
          this.goodsMaterialData.fileList.push({
            url: response.data.url,
            name: file.name,
            // 补充一些信息
            attachId: response.data.attachId,
            link: response.data.url,
            originalName: file.name,
          })
        }
      }
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
      // 修正：使用正确的索引来更新文件列表
      if (this.goodsMaterialData) {
        this.goodsMaterialData.fileList = fileList
      }
    },
    // 上传
    handleUpload(item) {
      console.log(item, '--item')
      this.goodsMaterialData = { ...item, fileList: [] }
      this.dialogVisible = true
    },
    // 返回
    goBack() {
      this.$router.$avueRouter.closeTag()
      this.$router.push({ path: '/Intermediation/index' })
    },
    async getDetailData() {
      const { data: resData } = await getDetailApi(this.$route.query.id)
      this.detailData = resData.data

      // 补充资料
      const { customerMaterial } = resData.data || {}
      this.creditFormData = JSON.parse(customerMaterial.data)
      this.customerMaterialList = JSON.parse(customerMaterial.creditForm)
      this.customerMaterialFormUpload = JSON.parse(customerMaterial.supplementMaterial).map(item => {
        item.uploadArr = item.uploadArr
          .map(itemed => {
            if (itemed.url) {
              const file = itemed.url.split('/')
              itemed.fileType = file[file.length - 1].split('.')[1].toLowerCase()
              return itemed
            }
          })
          .filter(Boolean)
        return item
      })

      // 已上传 - 调整数据结构以适配 FilePreviewHWP 组件
      this.capitalSupplementMaterialsList = resData.data.capitalSupplementMaterials
        .filter(item => item.status === 1)
        .map(item => {
          return {
            ...item,
            materialName: item.materialDesc,
            uploadArr: JSON.parse(item.materialFileJson) && JSON.parse(item.materialFileJson).map(subItem => ({
              url: subItem.fileUrl,
              fileName: subItem.fileName || '文件',
              fileType: subItem.fileUrl ? subItem.fileUrl.split('.').pop().toLowerCase() : '',
            })),
          }
        })

      // 待上传 - 调整数据结构以适配 FilePreviewHWP 组件
      this.capitalSupplementMaterialsUploadList = resData.data.capitalSupplementMaterials
        .filter(item => item.status === 0)
        .map(item => {
          return {
            ...item,
            materialName: item.materialDesc,
            uploadArr: [], // 待上传的资料暂时没有文件
          }
        })
    },
  },

  mounted() {
    this.getDetailData()
  },
}
</script>
<style lang="scss" scoped>
.member-info {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .member-info-left {
    display: flex;

    .member-info-left-left {
      margin-right: 40px;
    }
  }

  .label {
    color: #333;
    font-size: 14px;
  }

  .value {
    color: #409eff;
    font-size: 16px;
    font-weight: 700;
  }
}

.title {
  height: 22px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
  font-weight: 700;
}

.menu-box {
  display: flex;
  justify-content: center;
  align-items: center;

  .go-back {
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 100);
    color: rgba(0, 7, 42, 100);
    font-size: 14px;
    border: 1px solid rgba(187, 187, 187, 100);
    padding: 4px 10px;
    box-sizing: border-box;
    cursor: pointer;
    margin-right: 18px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.descriptions-for-box-title {
  margin-bottom: 5px;
  font-size: 15px;
  font-weight: bold;
  color: #333;
}
.descriptions-for-box-title-border {
  padding-top: 10px;
  border-top: dashed;
  border-color: #ccc;
  border-width: 1px;
}
</style>
