<template>
  <div class="financing-demand">
    <span class="top-line" />

    <div class="container" style="margin-bottom: 40px;">
      <!-- 融资方案 -->
      <div class="pledge-box">
        <div class="pledge-header">
          <span class="pledge-text-title">选择订单</span>
        </div>
        <div class="tradefor-information">
          <div
            class="tradefor-for"
            v-for="(item, index) in appliCationsData"
            :key="item.key"
          >
            <TradeforBox
              :ref="`tradeforBox${index}`"
              :items="item"
              :indexs="index"
              @deletes="deleteAppliCationsData"
            />
          </div>
          <div class="tradefor-for" v-if="!appliCationsData.length">
            <AddCard
              @create="handleCreate()"
              width="398px"
              height="185px"
              :title-name="'选择订单'"
            />
          </div>
        </div>
      </div>

      <!-- 融资方案 -->
      <div class="pledge-box" style="flex: 1; margin-left: 40px">
        <div class="pledge-header">
          <span class="pledge-text-title">{{ schemeDetailList.length > 1 ? '选择' : '' }}融资方案</span>
        </div>
        <div class="scheme-details">
          <div
            class="detail-item"
            :class="{ 'selected': selectedDetailIndex === index }"
            v-for="(item, index) in schemeDetailList"
            :key="index"
            @click="handleDetailItemClick(index, item)"
          >
            <div class="detail">
              <span class="detail-label-text">融资产品：</span>
              <span class="detail-label">{{ item.label }}</span>
            </div>
            <div class="detail-value">
              <a-statistic :precision="2" :value="item.value ?? 0">
                <template #prefix>
                  <span class="money-prefix">融资总额：￥</span>
                </template>
              </a-statistic>
            </div>
          </div>
          <div class="empty-state" v-if="!schemeDetailList.length">
            <el-empty :image-size="80" description="请先选择订单" />
          </div>
        </div>
      </div>
    </div>

    <div class="container" style="padding-bottom: 20px;">
      <!-- 方案详情 -->
      <div class="pledge-box" style="flex: 1; padding-bottom: 20px;">
        <div class="pledge-header">
          <span class="pledge-text-title">融资详情</span>
        </div>
        <a-table
          class="contract-custom-table"
          :columns="columns"
          :data-source="dataSource"
          :loading="tableLoading"
          :pagination="false"
          :locale="{
            emptyText: '请选择融资方案'
          }"
        >
          <template #bodyCell="{ column, text }">
            <template
              v-if="['availableCredit', 'quota', 'availableAmount'].includes(column.dataIndex)"
            >
              <a-statistic :precision="2" :value="text ?? 0">
                <template #prefix>
                  <span class="money-prefix">￥</span>
                </template>
              </a-statistic>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <!-- 融资金额 -->
    <div class="financing-amount">
      <div class="financing-top-amount">
        <div class="financing-left-amount">
          <div class="pledge-header">
            <span class="pledge-text-title">融资金额</span>
          </div>
          <div class="financing-money-all">
            <!-- <a-statistic :precision="2" :value="allNumMoney">
              <template #prefix>
                <span>￥</span>
              </template>
            </a-statistic> -->
            <a-input-number
              v-model:value="financingPrice"
              :min="0"
              :max="allNumMoney"
              :bordered="false"
              prefix="￥"
              placeholder="请输入融资金额"
              :formatter="numberFormatter"
              :parser="numberParser"
              @blur="financeValueBlur"
            />
          </div>
          <span class="line" />
          <div class="financing-hint">
            <MySvgIcon
              icon-class="icon-xinxi"
              style="fill: #8a94a6; font-size: 24px"
              targerUri
            ></MySvgIcon>
            <span class="financing-text-hint">按天算利息，可提前还款</span>
          </div>
        </div>

        <!-- 借多久 -->
        <div class="financing-right-amount">
          <div class="exhibition-box" @click="openLoanTerm()">
            <span class="exhibition-text">借多久？</span>
            <div class="exhibition-menu">
              <div v-if="loanPeriod">
                <span class="exhibition-text-menu" v-if="loadTermUnit === 2">
                  {{ loanPeriod }}个月
                </span>
                <span
                  class="exhibition-text-menu"
                  v-else-if="loadTermUnit === 1"
                >
                  {{ loanPeriod }}天
                </span>
              </div>
              <span v-else class="exhibition-text-menu">请选择</span>

              <span class="exhibition-do-menu">
                <MySvgIcon
                  icon-class="icon-youjiantou1"
                  style="fill: #8a94a6; font-size: 28px"
                  targerUri
                ></MySvgIcon>
              </span>
            </div>
          </div>

          <!-- 融资用途 -->
          <div class="exhibition-box" @click="purposeLoan()">
            <span class="exhibition-text">融资用途</span>
            <div class="exhibition-menu">
              <span class="exhibition-text-menu">
                {{ purposeLoanData.text || '请选择' }}
              </span>
              <span class="exhibition-do-menu">
                <MySvgIcon
                  icon-class="icon-youjiantou1"
                  style="fill: #8a94a6; font-size: 28px"
                  targerUri
                ></MySvgIcon>
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 还款试算 -->
      <div class="financing-bottom-amount" @click="handleExhibition()">
        <div class="exhibition-box">
          <span class="exhibition-text">怎么还？</span>
          <div class="exhibition-menu">
            <div class="exhibition-text-menu">
              <span v-if="filterNullStr" class="null-box">
                {{ filterNullStr }}
              </span>
              <span v-else class="text-box">
                {{ filterArr.filterTabBar[tabBarObj.tabBarIndex] }}
              </span>
              <span class="blue-text-box">查看还款试算</span>
            </div>
            <div class="exhibition-do-menu">
              <MySvgIcon
                icon-class="icon-youjiantou1"
                style="fill: #8a94a6; font-size: 28px"
                targerUri
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 按钮 -->
    <div class="financing-menu-box">
      <NButton
        class="blue button-item primary"
        type="info"
        round
        :bordered="true"
        :loading="submitLoading"
        @click="nextToPage"
      >
        <!-- TODO: 还未兼容手动放款 -->
        <span class="desc">下一步</span>
      </NButton>
    </div>

    <!-- 选择订单弹窗 -->
    <SchemeSelectDialog ref="SchemeSelectDialogRef" @allRecount="allRecount" />

    <!-- 借多久弹窗（期） -->
    <DialogTimeMonth
      ref="DialogTimeMonthRef"
      :loadTermObj="loadTermObj"
      :loanPeriod="loanPeriod"
      @setLoanPeriod="alterationLoanPeriod"
    />
    <!-- 借多久弹窗（天） -->
    <DialogTimeDays
      ref="DialogTimeDaysRef"
      :loadTermObj="loadTermObj"
      :loanPeriod="loanPeriod"
      @setLoanPeriod="alterationLoanPeriod"
    />

    <!-- 融资用途 -->
    <DialogPurposeLoan
      ref="DialogPurposeLoanRef"
      :purposeLoanList="purposeLoanList"
      :setpurposeLoanObj="purposeLoanData"
      @setData="setPurposeLoanData"
    />

    <!-- 还款试算弹窗 -->
    <DialogReimburSement
      ref="DialogReimburSementRef"
      :filterArr="filterArr"
      :tabBarObj="tabBarObj"
      :loadTermUnit="loadTermUnit"
      :queryParams="ReimburSementQueryParams"
      :chargeMethod="chargeMethod"
      @saveRepaymentType="saveRepaymentType"
    />
  </div>
</template>

<script lang="ts">
export default { name: 'financingDemand' }
</script>
<script setup lang="ts">
import { ref, computed, onMounted, reactive, Ref, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { min, maxBy, pick } from 'lodash'

import { PRODUCT_APPLI_CATIONS_API, PRODUCT_VERIFY_API } from '@/api/index.js'
import { requestDictMap } from '@/api/common/index'

import TradeforBox from './components/tradeforBox.vue'
import SchemeSelectDialog from './components/Dialog/SchemeSelectDialog.vue'
import AddCard from '@/components/BaseCard/addCard/index.vue'

import DialogTimeMonth from './components/Dialog/dialogTimeMonth.vue'
import DialogTimeDays from './components/Dialog/dialogTimeDays.vue'
import DialogPurposeLoan from './components/Dialog/dialogPurposeLoan.vue'
import DialogReimburSement from './components/Dialog/dialogReimburSement.vue'

import { NButton } from 'naive-ui'
import { message } from 'ant-design-vue'
import type { TableColumnsType } from 'ant-design-vue'

import dayjs from 'dayjs'

/**  ------------------ type ------------------ */
type Recordable<T = any> = Record<string, T>
type loadTermObjType = Partial<{ loadTermStart: number; loadTermEnd: number }>

/**  ------------------ 初始化 ------------------ */
// hooks
const store = useStore()
const route = useRoute()
const emit = defineEmits(['setCurrentUpFun'])

// 数字框格式化
const numberFormatter = (value: number) =>
  `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
const numberParser = (value: string) => value.replace(/\￥\s?|(,*)/g, '')
// Element ref
const SchemeSelectDialogRef = ref()
const DialogTimeMonthRef = ref()
const DialogTimeDaysRef = ref()
const DialogPurposeLoanRef = ref()
const DialogReimburSementRef = ref()

// default data
const columns: any = ref([
  {
    title: '资金产品名称',
    dataIndex: 'goodsName',
    align: 'left',
    width: 250,
  },
  {
    title: '资金方',
    dataIndex: 'capitalName',
    align: 'left',
    // width: 250,
  },
  {
    title: '融资金额',
    dataIndex: 'quota',
    align: 'left',
    width: 180,
  },
  // {
  //   title: '可用额度',
  //   dataIndex: 'availableCredit',
  //   align: 'center',
  // },
  {
    title: '剩余额度',
    dataIndex: 'availableAmount',
    align: 'left',
    width: 180,
  },
  {
    title: '保证金(%)',
    dataIndex: 'bondProportion',
    align: 'center',
    width: 140,
  },
  {
    title: '融资占比(%)',
    dataIndex: 'financingProportion',
    align: 'center',
    width: 140,
  },
])

// storage 数据
const planDetail = ref([]) // 方案详情
const dataSource: any = ref([]) // 表格数据
const tableLoading = ref(false) // 表格loading
const bindProductList = ref<Recordable[]>([]) // 关联的产品列表
const loadTermUnit = ref(2) // 1 天 2 月  分期还是随借随还
const loanPeriod = ref(0) // 借款期限
const loadTermObj = ref<loadTermObjType>({}) // 借款区间
const purposeLoanData = ref<{ text?: number; index?: number }>({}) // 融资用途
const purposeLoanList = ref<Recordable[]>([]) // 融资用途列表
const filterNullStr = ref<string | null>('请选择') // 还款试算为空时
const submitLoading = ref(false) // 提交loading
const billingMethod = ref<Recordable[]>([]) // 分期字典
const financingList = ref<Recordable[]>([])
const filterArr = reactive<{
  filterTabBar: string[]
  filterTabBarVal: string[]
}>({
  filterTabBar: [],
  filterTabBarVal: [],
}) // 还款试算内容
const tabBarObj = reactive({
  tabBarIndex: 0,
  tabBarIndexVal: void 0,
}) // 还款试算选中内容

const isStages = computed(() => loadTermUnit.value === 2) // 是否分期
const chargeMethod = computed(
  () => bindProductList.value[0]?.chargeMethod?.toString() ?? '1'
) // 还款方式

const appliCationsData = computed<Recordable[]>(
  () => store.getters['Product/appliCations']
) // 已选择的方案

// const allNumMoney = computed(
//   () => appliCationsData.value?.[0]?.financingAmount ?? 0
// ) // 可融资总金额
const allNumMoney = ref(0)

const financingPrice = ref<number | string>(allNumMoney.value || 0)

// 方案详情列表数据
const schemeDetailList: any = ref([])

// 选中的详情项索引
const selectedDetailIndex = ref<number | null>(null)

// watch(
//   () => allNumMoney.value,
//   () => (financingPrice.value = allNumMoney.value)
// )

// 还款试算参数
const ReimburSementQueryParams = computed(() => {
  const filterList = planDetail.value.filter(
    (item: Recordable) => item.quotaActual
  )

  const bol = filterList.length > 0
  let list = bol ? filterList : planDetail.value

  return list.map((item: Recordable) => {
    const { quota, quotaActual, product } = item

    // 获取当前产品详情
    const currentProduct = bindProductList.value.find(
      item => item.goodsId === product.id
    )

    // 如果产品的最大期限小于输入期限则拿产品最大期限
    const loanTerm = min([product.loadTermEnd, loanPeriod.value])

    return {
      financeAmount: bol ? quotaActual : quota,
      totalTerm: loanTerm,
      loanDay: loanTerm,
      goodsId: product.id,
      goodType: product.type,
      loadTermUnit: product.loadTermUnit,
      ...pick(currentProduct, ['annualInterestRate', 'enterpriseQuotaId']),
    }
  })
})

// 钩子函数
onMounted(async () => {
  financingPrice.value = 0
  store.commit('Product/setAppliCations', [])
  // 获取产品组可融资的关联产品（用于获取年利率与额度ID）
  getProductGroupBindProduct()
  // 获取数据字典
  await getDictData()
  // 数据回显
  getDataContent()
  // 获取融资数据 - 用于上一步之后拿融资数据进行处理
  getFinancingList()
})

/**
 * 数据回显
 */
const getDataContent = async (isGetPlanNo?: boolean) => {
  // 获取进度
  const planId = sessionStorage.getItem('planId') || route.query.planId
  if (!planId) return
  const { data: resData } = await PRODUCT_VERIFY_API.getByBusinessIdAndType3(
    planId
  )

  const { data } = resData
  const {
    planNo,
    financingAmount,
    actualAmount,
    totalOrderAmount,
    id,
    loadTerm,
    orderList
  } = data
  if (isGetPlanNo) {
    sessionStorage.setItem('planNo', planNo)
    return
  }
  const appliCations = [
    {
      planNo,
      financingAmount,
      financingAvailableAmount: totalOrderAmount,
      id,
    },
  ]

  store.commit('Product/setAppliCations', orderList)
  await allRecount(true) // 初始化数据

  // 回显选中的方案详情项
  if (schemeDetailList.value.length) {
    const selectedItem = schemeDetailList.value.find((item: any, index: number) => item.id === id)
    if (selectedItem) {
      handleDetailItemClick(0, selectedItem)
    }
  }

  // 回显金额
  financingPrice.value = actualAmount

  // 回显
  loanPeriod.value = loadTerm
  const curPurpose = purposeLoanList.value.find(
    (item: Recordable) => item.key == data.loanUsage
  )
  purposeLoanData.value = { index: curPurpose?.key, text: curPurpose?.value }

  // 还款试算回显
  if (data.loadTermUnit === 1) {
    saveRepaymentType({
      tabBarIndex: 0,
      filtArrKey: data.repaymentMode.toString(),
    })
  } else {
    billingMethod.value.forEach((item: Recordable, index: number) => {
      if (item.value == data.repaymentMode) {
        saveRepaymentType({ tabBarIndex: index, filtArrKey: item.key })
      }
    })
  }
}

const getFinancingList = async () => {
  const financingIds = sessionStorage.getItem('financingIds')
  if (!financingIds) return
  const { data: resData } =
    await PRODUCT_APPLI_CATIONS_API.financeApplyDetailList(financingIds)
  console.log('----------------融资列表', resData)
  financingList.value = resData.data.map(({ financeApply }: Recordable) => {
    return pick(financeApply, ['id', 'financeNo'])
  })
}

// 获取产品组可融资的关联产品（用于获取年利率与额度ID）
const getProductGroupBindProduct = async () => {
  const groupId = route.query.goodId
  const { data: resData } = await PRODUCT_APPLI_CATIONS_API.listByGroupId({
    groupId,
  })
  bindProductList.value = resData?.data ?? []
}

// 获取字典数据
const getDictData = async (): Promise<void> => {
  return new Promise(resolve => {
    const params = [
      // 借款用途字典
      { code: 'finance_apply_loan_usage', arr: purposeLoanList },
      // 分期字典
      { code: 'goods_billing_method', arr: billingMethod },
    ]

    // 兼容同步请求
    const promiseList = params.map(item => requestDictMap(item.code))
    Promise.all(promiseList).then(res => {
      res.forEach((item, index) => {
        const { data: resData } = item
        params[index].arr.value = (resData.data ?? []).map(
          (item: Recordable) => {
            return {
              key: item.dictKey,
              value: item.dictValue,
              id: item.id,
            }
          }
        )
      })
      resolve()
    })
  })
}

/**  ------------------ 方法 ------------------ */

/**
 * 处理详情项点击事件
 */
const schemeId = ref('') // 方案id
const handleDetailItemClick = async (index: number, item: any) => {
  // 如果点击的是同一项，则取消选中
  if (selectedDetailIndex.value === index) {
    // selectedDetailIndex.value = null
  } else {
    // selectedDetailIndex.value = index
  }
  selectedDetailIndex.value = index

  console.log('点击了详情项:', item.label, item.value)

  schemeId.value = item.id

  sessionStorage.setItem('planId', item.id)
  // 这里可以添加更多的点击处理逻辑
  tableLoading.value = true
  const { data: resData } = await PRODUCT_APPLI_CATIONS_API.pageWithProduct({
    planNo: item.planNo,
  })
  tableLoading.value = false
  const dataList = resData?.data ?? []
  planDetail.value = dataList
  dataSource.value = dataList.map((item: Recordable) => {
    const { quota, product, enterpriseQuota } = item
    return {
      quota,
      ...pick(product, ['goodsName', 'capitalName', 'availableCredit']),
      ...pick(enterpriseQuota, ['bondProportion', 'financingProportion', 'availableAmount']),
      // bondProportion: enterpriseQuota.bondProportion || '--',
      // financingProportion: enterpriseQuota.financingProportion || '--',
    }
  })
  console.log(dataSource.value, '--dataSource.value')
  const firstData = dataSource.value?.[0];
  if (firstData) {
    const baseColumns = [
      {
        title: '资金产品名称',
        dataIndex: 'goodsName',
        align: 'left',
        width: 250,
      },
      {
        title: '资金方',
        dataIndex: 'capitalName',
        align: 'left',
      },
      {
        title: '融资金额',
        dataIndex: 'quota',
        align: 'left',
        width: 180,
      },
      {
        title: '剩余额度',
        dataIndex: 'availableAmount',
        align: 'left',
        width: 180,
      },
    ];

    if (firstData.bondProportion !== undefined && firstData.bondProportion !== null) {
      columns.value = [...baseColumns, {
        title: '保证金（%）',
        dataIndex: 'bondProportion',
        align: 'left',
        width: 140,
      }];
    } else {
      columns.value = [...baseColumns, {
        title: '融资占比（%）',
        dataIndex: 'financingProportion',
        align: 'left',
        width: 140,
      }];
    }
  }

  financingPrice.value = item.value

  allNumMoney.value = item.value

  getDataContent(true)

  // 处理借多久逻辑
  loadLogic(dataList)

  // 还款试算初始化逻辑
  repaymentLogic()
}

/**
 * 输入完融资金额需要重新获取数据
 */
const financeValueBlur = async () => {
  const { goodId, planNo, planId } = route.query
  const params = {
    customerEnterAmount: financingPrice.value,
    goodsId: goodId,
    planNo: sessionStorage.getItem('planNo') || planNo,
    planId: sessionStorage.getItem('planId') || planId,
  }
  const { data: resData } =
    await PRODUCT_APPLI_CATIONS_API.planCalculateByAmount(params)
  const { data } = resData
  planDetail.value = data.financingPlanQuotaList.map((item: Recordable) => {
    const curQuota: Recordable =
      planDetail.value.find((cItem: Recordable) => cItem.id == item.id) ?? {}
    return {
      ...curQuota,
      financingPlanQuotaBindList: item.financingPlanQuotaBindList,
      quotaActual: item.quotaActual,
    }
  })
}

/**
 * @description: 打开选择方案弹窗
 * @return {*}
 */
const handleCreate = (): void => {
  SchemeSelectDialogRef.value.handleOpen()
  setTimeout(() => {
    SchemeSelectDialogRef.value.handreFresh() // 初始化数据
  }, 100)
}

/**
 * @description 选择方案回调
 * @return {*} Promise<void>
 */
const allRecount = async (notUpdatePlan?: boolean): Promise<void> => {
  const currentData = appliCationsData.value?.[0]

  console.log(currentData, '--currentData--')

  if (!currentData) return

  // sessionStorage.setItem('planId', currentData.id)

  // tableLoading.value = true
  // 根据方案获取融资详情
  let params = {
    orderId: currentData.id,
    groupId: route.query.goodId,
  }
  // const { data: resData } = await PRODUCT_APPLI_CATIONS_API.pageWithProduct({
  //   planNo: currentData.planNo,
  // })
  // if (notUpdatePlan) {
  //   store.commit('Product/setAppliCations', [])
  //   return
  // };

  const { data: resData } = await PRODUCT_APPLI_CATIONS_API.getByOrder(params)

  tableLoading.value = false

  const dataList = resData?.data ?? []

  // 保存原数据与表格数据
  // planDetail.value = dataList
  // dataSource.value = dataList.map((item: Recordable) => {
  //   const { quota, product } = item
  //   return {
  //     quota,
  //     ...pick(product, ['goodsName', 'capitalName', 'availableCredit']),
  //   }
  // })
  schemeDetailList.value = dataList.map((item: any) => {
    return {
      ...item,
      label: item.groupName,
      value: item.financingAmount,
    }
  })

  if (schemeDetailList.value.length === 1) {
    handleDetailItemClick(0, schemeDetailList.value[0])
  }

  // if (!notUpdatePlan) {
  //   // 更新金额
  //   financingPrice.value = allNumMoney.value
  //   getDataContent(true)
  // }

  // // 处理借多久逻辑
  // loadLogic(dataList)

  // // 还款试算初始化逻辑
  // repaymentLogic()
}

/**
 * 借多久逻辑处理
 * @param dataList 方案产品列表
 */
const loadLogic = (dataList: Recordable[]) => {
  // 获取借多久单位，取其中一个即可（后期可通过产品组的方式来获取）
  loadTermUnit.value = dataList[0]?.product?.loadTermUnit ?? 2

  // 获取方案里面的所有期间区间
  const loadList = dataList.map((item: Recordable) => {
    return pick(item.product, ['loadTermStart', 'loadTermEnd'])
  })

  // 获取最大值
  const getMaxBy = (name: keyof loadTermObjType) => {
    return maxBy<loadTermObjType>(loadList, name)?.[name]
  }

  // 根据方案来获取可选期限的区间
  loadTermObj.value['loadTermStart'] = getMaxBy('loadTermStart')
  loadTermObj.value['loadTermEnd'] = getMaxBy('loadTermEnd')
  // 丢失响应式，重新展开赋值（浅拷贝）
  loadTermObj.value = { ...loadTermObj.value }
}

/**
 * 还款试算逻辑初始化
 */
const repaymentLogic = () => {
  if (isStages.value) {
    // 分期
    filterArr.filterTabBar = billingMethod.value.map(item => item.value)
    filterArr.filterTabBarVal = billingMethod.value.map(item => item.key)
  } else {
    // 随借随还
    filterArr.filterTabBar = ['随借随还']
    filterArr.filterTabBarVal = ['-1']
  }
}
/** 设置融资用途数据 */
const setPurposeLoanData = (val: Recordable) => {
  purposeLoanData.value = val
}

/**
 * 删除选中方案
 */
const deleteAppliCationsData = (index: number) => {
  appliCationsData.value.splice(index, 1) // 删除arr

  // 清空表格与方案数据
  dataSource.value = []
  planDetail.value = []
  loanPeriod.value = 0
  financingPrice.value = ''

  // 重置选中状态
  selectedDetailIndex.value = null
  schemeDetailList.value = []

  store.commit('Product/setAppliCations', appliCationsData.value) // 保存修改
}

/**
 * 借多久的弹窗
 */
const openLoanTerm = () => {
  if (!Object.keys(loadTermObj.value).length) {
    message.warning('请先选择融资方案')
    return
  }
  const currentModal = isStages.value ? DialogTimeMonthRef : DialogTimeDaysRef
  currentModal.value.handleOpen() // 打开弹窗
}

/**
 * 融资用途弹窗
 */
const purposeLoan = () => {
  if (JSON.stringify(purposeLoanData.value) !== '{}') {
    DialogPurposeLoanRef.value.handleReset(purposeLoanData.value) // 设置之前已选index
  }
  DialogPurposeLoanRef.value.handleOpen() // 打开弹窗
}

/**
 * 设置借多久期限
 */
type DayObj = { day: number }
const alterationLoanPeriod = (num: number | DayObj) => {
  loanPeriod.value =
    (isStages.value ? (num as number) : (num as DayObj)?.day) ?? 0
}

/**
 * 还款试算弹窗
 */
const handleExhibition = () => {
  if (verifyCheck()) {
    DialogReimburSementRef.value.handleOpen() // 打开弹窗
    DialogReimburSementRef.value.onload() // 初始化数据
  }
}

/**
 * 提交校验
 */
const verifyCheck = (type?: boolean) => {
  const rules = [
    { rule: !appliCationsData.value.length, message: '未选择方案' },
    { rule: !+financingPrice.value, message: '未填写融资金额' },
    { rule: !loanPeriod.value, message: '未选择融资期限' },
    { rule: !purposeLoanData.value.index, message: '未选择融资用途' },
    { rule: filterNullStr.value && type, message: '未选择还款方式' },
  ]

  for (const { rule, message: ruleMsg } of rules) {
    if (rule) {
      message.warning(ruleMsg)
      return false
    }
  }

  return true
}

// 设置还款试算参数
const saveRepaymentType = (obj: Recordable) => {
  tabBarObj.tabBarIndex = obj.tabBarIndex
  tabBarObj.tabBarIndexVal = obj.filtArrKey
  filterNullStr.value = null // 清除请选择文案
}

/**
 * 提交
 */
const nextToPage = async () => {
  if (!verifyCheck(true)) return
  submitLoading.value = true

  const filterList = planDetail.value.filter(
    (item: Recordable) => item.quotaActual
  )

  const bol = filterList.length > 0
  let list = bol ? filterList : planDetail.value

  // 遍历产品参数
  const financeApplyHandlerDTOList = list.map((item: Recordable) => {
    const {
      customerGoodsId,
      product,
      quota,
      quotaActual,
      limitId,
      id,
      financeId,
    } = item

    const curFinanceData = financingList.value.find(
      item => item.id === financeId
    )

    // 获取当前产品详情
    const currentProduct = bindProductList.value.find(
      item => item.goodsId === product.id
    )

    // 如果产品的最大期限小于输入期限则拿产品最大期限
    const loanTerm = min([product.loadTermEnd, loanPeriod.value])

    const goodsType = product.type
    return {
      receivableFinanceDTO: {
        salesContractDetails: [
          {
            amount: bol ? quotaActual : quota,
            saleContractId: limitId,
          },
        ],
      },
      financeApply: {
        ...curFinanceData,
        ...pick(product, ['lendingMethod', 'chargeMethod', 'loadTermUnit']),
        customerGoodsId,
        goodsId: product.id,
        loadTerm: loanTerm,
        loanUsage: purposeLoanData.value.index,
        repaymentMode: tabBarObj.tabBarIndexVal,
        // financeNo: null,
        // id: null,
        goodsType,
      },
      goodsType,
      planQuotaId: id,
      ...pick(product, ['lendingMethod', 'chargeMethod']),
      costCalculusDto: {
        enterpriseQuotaId: currentProduct?.enterpriseQuotaId,
        chargePoint: 8,
        customerGoodsId,
        totalTerm: loanTerm,
        loanDay: loanTerm,
        startTime: dayjs().format('YYYY-MM-DD'),
      },
    }
  })

  const params = {
    // id: appliCationsData.value[0].id,
    id: schemeId.value,
    loanUsage: purposeLoanData.value.index,
    loadTerm: loanPeriod.value,
    loadTermUnit: loadTermUnit.value,
    repaymentMode: tabBarObj.tabBarIndexVal,
    financingPlanQuotaList: planDetail.value,
    financeApplyHandlerDTOList,
    actualAmount: financingPrice.value,
  }
  const { data: resData } = await PRODUCT_APPLI_CATIONS_API.financeApplySubmit2(
    params
  )
  sessionStorage.setItem('financingIds', resData.data)

  submitLoading.value = false

  emit('setCurrentUpFun', null)
}
</script>

<style lang="scss" scoped>
.financing-demand {
  max-width: 1400px;
  position: relative;
  margin: auto;
  box-sizing: border-box;

  .top-line {
    width: 100%;
    // height: 1px;
    display: block;
    background: #f1f2f4;
    margin-top: 40px;
    // margin-bottom: 48px;
  }

  .container {
    width: 1400px;
    display: flex;

    .pledge-box {
      width: 440px;
      // height: 464px;
      background: #ffffff;
      box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
      border-radius: 16px;
      border: 1px solid #efefef;
      padding: 20px 20px 5px;
      box-sizing: border-box;

      .pledge-header {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 20px;

        .pledge-text-title {
          height: 32px;
          font-size: 24px;
          @include family-PingFangSC-Semibold;
          font-weight: 500;
          color: #0a1f44;
          line-height: 32px;
        }

        .pledge-Vertical {
          width: 1px;
          height: 22px;
          background: #b5bbc6;
          display: inline-block;
          margin-left: 12px;
          margin-right: 12px;
        }

        .pledge-price-box {
          height: 24px;
          font-size: 16px;
          @include family-PingFangSC-Semibold;
          font-weight: 500;
          color: #8a94a6;
          line-height: 24px;
          display: flex;

          .pledge-num-price {
            :deep(.ant-statistic-content) {
              font-size: 16px;
              font-weight: 500;
              color: #8a94a6;
              line-height: 24px;

              .ant-statistic-content-prefix {
                margin-right: -1px;
              }
            }
          }
        }
      }

      .tradefor-information {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: center;

        .tradefor-for {
          margin-bottom: 38px;
          margin-right: 38px;

          & > .base-card-item {
            margin-top: 0;
            margin-right: 0;
          }

          &:nth-child(3n) {
            margin-right: 0;
          }
        }
      }

      .contract-custom-table {
        margin-top: 24px;

        // 表格
        :deep(.ant-table) {
          // 去除表头分隔线
          .ant-table-thead
            > tr
            > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
            content: none;
          }

          .ant-table-row {
            cursor: pointer;
          }

          .ant-table-thead {
            th {
              padding: 0 12px;
              height: 40px;
              line-height: 40px;
              background-color: #f8f9fb;
              font-size: 14px;
              @include family-PingFangSC-Medium;
              font-weight: 500;
              color: #8a94a6;
            }
          }

          .ant-table-tbody {
            // 鼠标停留背景色
            tr.ant-table-row:hover > td {
              background: #f5faff;
            }

            // 清除无数据时的边框
            .ant-table-placeholder > td {
              border: none;
            }

            td {
              padding: 6px 12px;
              height: 48px;
              line-height: 1.24;
              font-size: 14px;
              @include family-PingFangSC-Semibold;
              font-weight: 500;
              color: #0a1f44;
              // color: #8a94a6;

              .ant-statistic-content {
                line-height: 1.24;
                font-size: 14px;
                @include family-PingFangSC-Semibold;
                font-weight: 500;
                color: #0a1f44;

                .ant-statistic-content-prefix {
                  margin-right: 0px;
                }
              }
            }
          }
        }
      }

      .scheme-details {
        overflow-y: auto;
        overflow-x: hidden;
        height: 185px;
        /* 滚动条整体样式 - Chrome、Safari、Edge */
        &::-webkit-scrollbar {
          width: 8px; /* 滚动条宽度 */
        }

        /* 滚动条轨道 - Chrome、Safari、Edge */
        &::-webkit-scrollbar-track {
          background: #f1f1f1; /* 轨道背景颜色 */
          border-radius: 4px; /* 轨道圆角 */
        }

        /* 滚动条滑块 - Chrome、Safari、Edge */
        &::-webkit-scrollbar-thumb {
          background: #888; /* 滑块颜色 */
          border-radius: 4px; /* 滑块圆角 */
        }

        /* 滚动条滑块悬停状态 - Chrome、Safari、Edge */
        &::-webkit-scrollbar-thumb:hover {
          background: #555; /* 悬停时滑块颜色 */
        }

        /* 滚动条样式 - Firefox */
        scrollbar-width: thin; /* 滚动条宽度 */
        scrollbar-color: #888 #f1f1f1; /* 滑块颜色和轨道颜色 */

        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 28px;
          border-bottom: 1px solid #f1f2f4;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 8px;
          margin-bottom: 2px;
          box-sizing: border-box;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background-color: #f5faff;
            transform: translateX(2px);
          }

          &.selected {
            background-color: #e6f4ff;
            border-color: #0c65fd;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
            border-bottom: 1px solid #0c65fd;

            .detail-label {
              color: #0c65fd;
              font-weight: 600;
            }

            .detail-value {
              color: #e81123;
              font-weight: 600;
            }

            .detail-label-text {
              font-size: 20px;
              font-weight: 700;
            }
          }

          .detail {
            font-size: 20px;
            // font-weight: 700;
            color: #262626;
          }

          .detail-label {
            font-size: 20px;
            font-weight: 500;
            color: #262626;
            line-height: 20px;
          }

          .detail-value {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            line-height: 20px;

            .money-prefix {
              font-size: 20px;
            }
          }
        }

        .empty-state {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100px;
          color: #8a94a6;
          font-size: 14px;
        }
      }
    }
  }

  .financing-amount {
    display: flex;
    flex-direction: column;

    .financing-top-amount {
      margin-top: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: context-menu;

      .financing-left-amount {
        width: 680px;
        height: 262px;
        background: #ffffff;
        box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
        border-radius: 16px;
        border: 1px solid #efefef;
        padding: 40px 40px 24px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
        cursor: context-menu;

        .pledge-header {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 5px;

          .pledge-text-title {
            height: 32px;
            font-size: 24px;
            @include family-PingFangSC-Semibold;
            font-weight: 500;
            color: #0a1f44;
            line-height: 32px;
          }

          .pledge-Vertical {
            width: 1px;
            height: 22px;
            background: #b5bbc6;
            display: inline-block;
            margin-left: 12px;
            margin-right: 12px;
          }

          .pledge-price-box {
            height: 24px;
            font-size: 16px;
            @include family-PingFangSC-Semibold;
            font-weight: 500;
            color: #8a94a6;
            line-height: 24px;
            display: flex;

            .pledge-num-price {
              :deep(.ant-statistic-content) {
                font-size: 16px;
                font-weight: 500;
                color: #8a94a6;
                line-height: 24px;

                .ant-statistic-content-prefix {
                  margin-right: -1px;
                }
              }
            }
          }
        }

        .financing-money-all {
          :deep(.ant-statistic-content) {
            .ant-statistic-content-prefix {
              font-size: 40px;
            }

            .ant-statistic-content-value {
              font-size: 64px;
            }
          }
          :deep(.ant-input-number-affix-wrapper-borderless) {
            width: 100%;
            .ant-input-number-prefix {
              font-size: 40px;
            }
            .ant-statistic-content-value {
              font-size: 64px;
            }
          }
          :deep(.ant-input-number) {
            width: 100%;
            height: 90px;
            margin: 10px 0;
            line-height: 90px;
            font-size: 64px;
            .ant-input-number-input {
              height: 90px;
            }
            .ant-input-number-handler-wrap {
              display: none;
            }
          }
          :deep(.ant-input-number-disabled) {
            background-color: transparent;
          }
        }

        .line {
          width: 100%;
          height: 1px;
          display: block;
          background: #f1f2f4;
          margin-top: 10px;
        }

        .financing-hint {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          margin-top: 24px;

          .financing-text-hint {
            height: 24px;
            font-size: 16px;
            @include family-PingFangSC-Semibold;
            font-weight: 400;
            color: #8a94a6;
            line-height: 24px;
            margin-left: 4px;
          }
        }

        &::before {
          content: '';
          display: inline-block;
          position: absolute;
          width: 105px;
          height: 145px;
          top: -25px;
          left: -28px;
          background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
          opacity: 0.14;
          border-radius: 150px;
          filter: blur(30px);
        }

        &::after {
          content: '';
          display: inline-block;
          position: absolute;
          width: 210px;
          height: 205px;
          bottom: -74px;
          right: -49px;
          background: linear-gradient(129deg, #ffffff 0%, #0c66ff 100%);
          opacity: 0.14;
          border-radius: 150px;
          filter: blur(13px);
        }
      }

      .financing-right-amount {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-end;
        height: 262px;

        .exhibition-box {
          width: 680px;
          height: 112px;
          background: #ffffff;
          box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
          border-radius: 16px;
          border: 1px solid #efefef;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 40px;
          box-sizing: border-box;
          cursor: pointer;

          .exhibition-text {
            height: 32px;
            font-size: 24px;
            @include family-PingFangSC-Semibold;
            font-weight: 500;
            color: #0a1f44;
            line-height: 32px;
          }

          .exhibition-menu {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .exhibition-text-menu {
              height: 32px;
              font-size: 24px;
              @include family-PingFangSC-Semibold;
              font-weight: 500;
              color: #8a94a6;
              line-height: 32px;
            }
          }
        }
      }
    }

    .financing-bottom-amount {
      .exhibition-box {
        width: 100%;
        height: 112px;
        background: #ffffff;
        box-shadow: 0px 12px 24px 0px rgba(10, 31, 68, 0.04);
        border-radius: 16px;
        border: 1px solid #efefef;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 40px;
        box-sizing: border-box;
        margin-top: 40px;
        cursor: pointer;

        .exhibition-text {
          height: 32px;
          font-size: 24px;
          @include family-PingFangSC-Semibold;
          font-weight: 500;
          color: #0a1f44;
          line-height: 32px;
        }

        .exhibition-menu {
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .exhibition-text-menu {
            font-size: 24px;
            @include family-PingFangSC-Semibold;
            font-weight: 500;
            color: #8a94a6;
            line-height: 32px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-end;

            .null-box {
              text-align: right;
              color: #8a94a6;
              font-weight: 500;
            }

            .text-box {
              width: 82px;
              height: 28px;
              font-size: 20px;
              font-weight: 400;
              color: #0a1f44;
              line-height: 28px;
            }

            .blue-text-box {
              width: 96px;
              height: 24px;
              font-size: 16px;
              font-weight: 400;
              color: #0d55cf;
              line-height: 24px;
            }
          }
        }
      }
    }
  }

  .financing-menu-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    margin-top: 48px;
    margin-bottom: 48px;

    :deep(.n-button) {
      width: 400px;
      height: 40px;
    }
  }
}
</style>
