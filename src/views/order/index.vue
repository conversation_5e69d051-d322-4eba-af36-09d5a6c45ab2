<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleCreate">创建订单</el-button>
      </template>
      <template slot="orderAmount" slot-scope="{ row }">
        <span>{{ row.orderAmount | formatMoney }}</span>
      </template>
      <template slot="actualFinancingAmount" slot-scope="{ row }">
        <span>{{ row.actualFinancingAmount | formatMoney }}</span>
      </template>
      <template slot="goodsPrice" slot-scope="{ row }">
        <span>{{ row.goodsPrice | formatMoney }}</span>
      </template>
    </avue-crud>

    <!-- 创建订单弹窗 -->
    <el-dialog
      title="创建订单"
      :visible.sync="createOrderDialog"
      width="60%"
      :close-on-click-modal="true"
      append-to-body
      top="5vh"
    >
      <el-form :model="orderForm" ref="orderForm" :rules="orderRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="融资企业" prop="companyId">
              <el-select
                v-model="orderForm.companyId"
                placeholder="请选择融资企业"
                filterable
                style="width: 100%"
                @change="handleCompanyChange"
              >
                <el-option
                  v-for="item in financingCompanyList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplierId">
              <el-select v-model="orderForm.supplierId" placeholder="请选择供应商" filterable style="width: 100%">
                <el-option v-for="item in supplierList" :key="item.id" :label="item.supperName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品名称" prop="goodsName">
              <el-select
                v-model="orderForm.goodsName"
                placeholder="请选择商品名称"
                filterable
                allow-create
                clearable
                style="width: 100%"
                @change="goodsChange"
              >
                <el-option v-for="item in goodsList" :key="item.id" :label="item.name" :value="item.name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-select
                v-model="orderForm.unit"
                placeholder="请选择商品单位"
                filterable
                allow-create
                clearable
                style="width: 100%"
              >
                <el-option v-for="item in unitList" :key="item.id" :label="item.unitName" :value="item.unitName" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="quantity">
              <el-input-number
                v-model="orderForm.quantity"
                :min="1"
                :controls="false"
                placeholder="请输入数量"
                style="width: 100%"
                @change="calculateOrderAmount"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品图片" prop="goodsLogo">
              <el-upload
                class="avatar-uploader"
                action="/api/blade-resource/oss/endpoint/put-file-kv"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div v-if="imageUrl" class="avatar-delete" @click="deleteAvatar"><i class="el-icon-delete"></i> 删除</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品价格" prop="goodsPrice">
              <el-input-number
                v-model="orderForm.goodsPrice"
                :precision="2"
                :min="0"
                :controls="false"
                :value-on-clear="undefined"
                :step-strictly="false"
                placeholder="请输入商品价格"
                style="width: 100%"
                @change="calculateOrderAmount"
              />
              <div style="margin-top: 5px; display: flex; gap: 4px">
                <el-button size="mini" @click="adjustPrice(-100000)">-10万</el-button>
                <el-button size="mini" @click="adjustPrice(-10000)">-1万</el-button>
                <el-button size="mini" @click="adjustPrice(10000)">+1万</el-button>
                <el-button size="mini" @click="adjustPrice(100000)">+10万</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单金额" prop="orderAmount">
              <el-input-number
                v-model="orderForm.orderAmount"
                :precision="2"
                :min="0"
                :controls="false"
                :value-on-clear="undefined"
                :step-strictly="false"
                placeholder="自动计算或手动输入"
                style="width: 100%"
              />
              <div style="margin-top: 5px; display: flex; gap: 4px">
                <el-button size="mini" @click="adjustOrderAmount(-100000)">-10万</el-button>
                <el-button size="mini" @click="adjustOrderAmount(-10000)">-1万</el-button>
                <el-button size="mini" @click="adjustOrderAmount(10000)">+1万</el-button>
                <el-button size="mini" @click="adjustOrderAmount(100000)">+10万</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际融资金额" prop="actualFinancingAmount">
              <el-input-number
                v-model="orderForm.actualFinancingAmount"
                :precision="2"
                :min="0"
                :controls="false"
                :value-on-clear="undefined"
                :step-strictly="false"
                placeholder="自动计算或手动输入"
                style="width: 100%"
              />
              <div style="margin-top: 5px; display: flex; gap: 4px">
                <el-button size="mini" @click="adjustFinancingAmount(-100000)">-10万</el-button>
                <el-button size="mini" @click="adjustFinancingAmount(-10000)">-1万</el-button>
                <el-button size="mini" @click="adjustFinancingAmount(10000)">+1万</el-button>
                <el-button size="mini" @click="adjustFinancingAmount(100000)">+10万</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务类型" prop="scenarioType">
              <el-radio-group v-model="orderForm.scenarioType" style="width: 100%">
                <el-radio-button :label="1">一对多</el-radio-button>
                <el-radio-button :label="2">多对一</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交货时间" prop="deliverTime">
              <el-date-picker
                v-model="orderForm.deliverTime"
                type="date"
                placeholder="请选择交货时间"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格">
              <el-input v-model="orderForm.spec" placeholder="请输入商品规格" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="createOrderDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitOrder">确认</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  createOrder,
  getFinancingCompanyList,
  getList,
  getSupplierList,
  getAllUpShelfCommodityList,
  commodityunitAll,
} from '@/api/order/index'

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      // 创建订单相关
      createOrderDialog: false,
      submitLoading: false,
      supplierList: [],
      financingCompanyList: [],
      orderForm: {
        orderAmount: undefined,
        companyId: null,
        companyName: '',
        scenarioType: 1,
        orderType: 4,
        goodsName: '',
        goodsPrice: undefined,
        unit: '件',
        quantity: 1,
        spec: '默认规格',
        deliverTime: '',
        goodsLogo: '',
        actualFinancingAmount: undefined,
        supplierId: null,
      },
      orderRules: {
        companyId: [{ required: true, message: '请选择融资企业', trigger: 'change' }],
        supplierId: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        goodsName: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        goodsPrice: [{ required: true, message: '请输入商品价格', trigger: 'blur' }],
        quantity: [{ required: true, message: '请输入数量', trigger: 'blur' }],
        unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
        orderAmount: [{ required: true, message: '请输入订单金额', trigger: 'blur' }],
        actualFinancingAmount: [{ required: true, message: '请输入实际融资金额', trigger: 'blur' }],
        scenarioType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        deliverTime: [{ required: false, message: '请选择交货时间', trigger: 'change' }],
      },
      option: {
        // height: 'auto',
        headerAlign: 'left',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        dialogClickModal: false,
        searchIndex: 3,
        searchIcon: true,
        menu: false,
        menuWidth: 80,
        columnBtn: false,
        column: [
          {
            label: '订单编号',
            prop: 'orderNo',
            search: true,
          },
          {
            label: '订单金额',
            prop: 'orderAmount',
          },
          {
            label: '融资需求金额',
            prop: 'actualFinancingAmount',
          },
          {
            label: '客户名称',
            prop: 'companyName',
            search: true,
          },
          {
            label: '商品名称',
            prop: 'goodsName',
          },
          {
            label: '商品单价',
            prop: 'goodsPrice',
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            dicData: [
              { label: '待使用', value: 0 },
              { label: '使用中', value: 1 },
            ],
            search: true,
          },
          {
            label: '创建时间',
            prop: 'createTime',
          },
        ],
      },
      data: [],
      hasJsonFlag: true,

      goodsList: [],
      unitList: [],
      imageUrl: '',
    }
  },
  methods: {
    // 头像logo
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw)
      this.orderForm.goodsLogo = res.data.url
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    deleteAvatar() {
      this.imageUrl = ''
      // 如果需要清除表单中的相关字段，也可以在这里处理
      // 例如: this.orderForm.goodsLogo = ''
      this.orderForm.goodsLogo = ''
    },
    // 获取单位
    goodsChange(val) {
      // 查找选中的商品
      const selectedGoods = this.goodsList.find(item => val === item.name)

      if (selectedGoods) {
        // 回显图片
        this.imageUrl = selectedGoods.img
        this.orderForm.goodsLogo = selectedGoods.img

        // 根据商品的unitId查找对应的单位名称
        const selectedUnit = this.unitList.find(unit => unit.id === selectedGoods.unitId)

        if (selectedUnit) {
          this.orderForm.unit = selectedUnit.unitName
        }
      }
    },
    // 商品列表
    async getAllUpShelfCommodityListFunc() {
      const { data } = await getAllUpShelfCommodityList()
      this.goodsList = data.data
    },
    // 商品单位
    async commodityunitAllFunc() {
      const { data } = await commodityunitAll()
      this.unitList = data.data
    },
    // 创建订单
    handleCreate() {
      this.loadSupplierList()
      this.getAllUpShelfCommodityListFunc()
      this.loadFinancingCompanyList()
      this.commodityunitAllFunc()
      this.createOrderDialog = true
    },
    // 计算订单金额和实际融资金额
    calculateOrderAmount() {
      if (this.orderForm.goodsPrice && this.orderForm.quantity) {
        const amount = Number((this.orderForm.goodsPrice * this.orderForm.quantity).toFixed(2))
        this.orderForm.orderAmount = amount
        // 实际融资金额默认等于订单金额
        this.orderForm.actualFinancingAmount = amount
      }
    },
    // 调整商品价格
    adjustPrice(amount) {
      const currentPrice = this.orderForm.goodsPrice || 0
      const newPrice = Math.max(0, currentPrice + amount)
      this.orderForm.goodsPrice = Number(newPrice.toFixed(2))
      this.calculateOrderAmount()
    },
    // 调整订单金额
    adjustOrderAmount(amount) {
      const currentAmount = this.orderForm.orderAmount || 0
      const newAmount = Math.max(0, currentAmount + amount)
      this.orderForm.orderAmount = Number(newAmount.toFixed(2))
    },
    // 调整实际融资金额
    adjustFinancingAmount(amount) {
      const currentAmount = this.orderForm.actualFinancingAmount || 0
      const newAmount = Math.max(0, currentAmount + amount)
      this.orderForm.actualFinancingAmount = Number(newAmount.toFixed(2))
    },
    // 加载供应商列表
    loadSupplierList() {
      const params = {
        current: 1,
        size: 100,
      }

      getSupplierList(params)
        .then(res => {
          // 根据实际的数据结构解析
          let data = []
          if (res.data && res.data.data && res.data.data.records) {
            data = res.data.data.records
          }

          // 过滤和处理数据
          if (Array.isArray(data)) {
            this.supplierList = data.filter(item => {
              return item && item.id
            })
          } else {
            this.supplierList = []
          }
        })
        .catch(err => {
          this.supplierList = []
        })
    },
    // 加载融资企业列表
    loadFinancingCompanyList() {
      const params = {
        current: 1,
        size: 100,
        isCoreOrFront: 2,
      }
      getFinancingCompanyList(params)
        .then(res => {
          const data = res.data.data.records || []
          this.financingCompanyList = Array.isArray(data) ? data.filter(item => item && item.id) : []
        })
        .catch(err => {
          this.financingCompanyList = []
        })
    },
    // 融资企业选择变化
    handleCompanyChange(companyId) {
      const company = this.financingCompanyList.find(item => item.id === companyId)
      if (company) {
        this.orderForm.companyName = company.name
      }
    },
    // 提交订单
    handleSubmitOrder() {
      this.$refs.orderForm.validate(valid => {
        if (valid) {
          if (!this.imageUrl) {
            return this.$message.warning('请上传商品图片')
          }

          this.submitLoading = true
          console.log(this.orderForm, '--this.orderForm')
          createOrder(this.orderForm)
            .then(res => {
              this.$message.success('订单创建成功')
              this.createOrderDialog = false
              this.resetOrderForm()
              this.refreshChange()
            })
            .catch(() => {
              // this.$message.error('订单创建失败')
            })
            .finally(() => {
              this.submitLoading = false
            })
        }
      })
    },
    // 重置订单表单
    resetOrderForm() {
      this.$refs.orderForm.resetFields()
      this.orderForm = {
        orderAmount: undefined,
        companyId: null,
        companyName: '',
        scenarioType: 1,
        orderType: 4,
        goodsName: '',
        goodsPrice: undefined,
        unit: '件',
        quantity: 1,
        spec: '默认规格',
        deliverTime: '',
        goodsLogo: '',
        actualFinancingAmount: undefined,
        supplierId: null,
      }
      this.imageUrl = ''
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  },
  watch: {
    createOrderDialog(val) {
      if (!val) {
        // 弹窗关闭时重置表单
        this.$nextTick(() => {
          this.resetOrderForm()
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #8c939d;
  width: 160px;
  height: 160px;
  line-height: 160px;
  text-align: center;
}
.avatar {
  width: 160px;
  height: 160px;
  display: block;
}
.avatar-delete {
  color: #ff4d4f;
  font-size: 12px;
  cursor: pointer;
  text-align: left;
  margin-top: 5px;
}

.avatar-delete:hover {
  color: #ff7875;
}
</style>
