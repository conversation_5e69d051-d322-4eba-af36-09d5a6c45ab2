import request from '@/router/axios'

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-plan/web-back/tradingOrderData/list',
    method: 'get',
    params: {
      current,
      size,
      ...params,
    },
  })
}

// 创建订单
export const createOrder = (data) => {
  return request({
    url: '/api/blade-plan/web-back/tradingOrderData/createOrder',
    method: 'post',
    data
  })
}

// 获取供应商列表
export const getSupplierList = (params) => {
  return request({
    url: '/api/blade-customer/web-back/customer/customersupplier/list',
    method: 'get',
    params
  })
}

// 获取融资企业列表
export const getFinancingCompanyList = (params) => {
  return request({
    url: '/api/blade-customer/web-back/customer/customerFrontUserType/customerFrontUserList',
    method: 'get',
    params
  })
}
// 获取商品列表
export const getAllUpShelfCommodityList = (params) => {
  return request({
    url: '/api/blade-commodity/web-back/commoditylist/getAllUpShelfCommodityList',
    method: 'get',
    params
  })
}
// 获取单位列表
export const commodityunitAll = (params) => {
  return request({
    url: '/api/blade-commodity/web-back/commodityunit/all',
    method: 'get',
    params
  })
}
