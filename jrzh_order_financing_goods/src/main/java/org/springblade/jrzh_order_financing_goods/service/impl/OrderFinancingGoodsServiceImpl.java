/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.jrzh_order_financing_goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.enums.GoodsEnum;
import org.springblade.common.enums.GoodsTypeEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.customer.constant.ProcessConstantExtend;
import org.springblade.jrzh_order_financing_goods.mapper.OrderFinancingGoodsMapper;
import org.springblade.jrzh_order_financing_goods.service.IOrderFinancingGoodsService;
import org.springblade.jrzh_order_financing_goods.wrapper.OrderFinancingGoodsWrapper;
import org.springblade.jrzh_order_financing_goods_api.entity.OrderFinancingGoods;
import org.springblade.jrzh_order_financing_goods_api.vo.OrderFinancingGoodsVo;
import org.springblade.othersapi.riskorderapi.dto.FinalApproveAmount;
import org.springblade.process.constant.ProcessConstant;
import org.springblade.product.common.constant.DepositYNEnum;
import org.springblade.product.common.dto.ProductDTO;
import org.springblade.product.common.entity.Product;
import org.springblade.product.common.entity.ProductManager;
import org.springblade.product.common.vo.ProductVO;
import org.springblade.product.moudle.pubproduct.service.IProductConfigInfoHandler;
import org.springblade.product.moudle.pubproduct.service.IProductManagerService;
import org.springblade.resource.service.IParamService;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.utils.UserUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 应收账款 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-24
 */
@Service
@RequiredArgsConstructor
public class OrderFinancingGoodsServiceImpl extends BaseServiceImpl<OrderFinancingGoodsMapper, OrderFinancingGoods> implements IOrderFinancingGoodsService {
    private final IParamService paramService;
    private final RemoteDeptSearchService deptService;
    private final List<IProductConfigInfoHandler> productConfigInfoHandlers;
    private final IProductManagerService productManagerService;

    @Override
    public List<Product> selectList(Map<String, Object> product, List<Long> ids) {
        return leftJoinProductManager(Condition.getMPJLambdaWrapper(product, OrderFinancingGoods.class)).in(ObjectUtil.isNotEmpty(ids), OrderFinancingGoods::getId, ids)
                .list(Product.class);
//
//                .selectAll(Goods.class)
//                .leftJoin(ProductManager.class, ProductManager::getProductId, Goods::getId)
//                .selectAs(ProductManager::getCapitalLogo,Product::getCapitalLogo)
//                .selectAs(ProductManager::getCapitalName,Product::getCapitalName)

//        List<Goods> list = list(Condition.getQueryWrapper(product, Goods.class).lambda());
//        if (CollUtil.isNotEmpty(list)) {
//            return BeanUtil.copyToList(list, Product.class);
//        }
//        return null;
    }

    private MPJLambdaWrapper<OrderFinancingGoods> leftJoinProductManager(MPJLambdaWrapper<OrderFinancingGoods> wrapper) {
        return wrapper.selectAll(OrderFinancingGoods.class)
                .leftJoin(ProductManager.class, ProductManager::getProductId, OrderFinancingGoods::getId)
                .selectAs(ProductManager::getCapitalLogo, Product::getCapitalLogo)
                .selectAs(ProductManager::getCapitalName, Product::getCapitalName);
    }

    @Override
    public IPage<ProductVO> selectGoodsPage(Map<String, Object> product, Query query) {
        IPage<ProductVO> page = leftJoinProductManager(Condition.getMPJLambdaWrapper(product, OrderFinancingGoods.class))
                .page(Condition.getPage(query), ProductVO.class);
        if (page.getTotal() <= 0) {
            return page;
        }
        List<ProductVO> records = page.getRecords();
        // 从产品列表中获取用户id，并查询用户
        Map<Long, String> userNameMap = UserUtils.mapUserName(StreamUtil.map(records, ProductVO::getUpdateUser));
        // 查询资方信息
        Map<Long, Dept> deptMap = UserUtils.mapDept(StreamUtil.map(records, ProductVO::getCapitalId));
        // 组装数据
        records = records.stream().peek(goodsVO -> {
            // 设置资金方logo和名称
            Dept dept = deptMap.get(goodsVO.getCapitalId());
            if (Objects.nonNull(dept)) {
                goodsVO.setCapitalLogo(dept.getLogoSrc());
                goodsVO.setCapitalName(dept.getDeptName());
            }
            // 设置操作人名称
            goodsVO.setOperator(userNameMap.getOrDefault(goodsVO.getUpdateUser(), ""));
        }).collect(Collectors.toList());
        page.setRecords(records);
        return page;
    }


    @Override
    public Product detailBase(Long id) {
        Product product = OrderFinancingGoodsWrapper.build().entityProduct(getById(id));
        settingPMInfo(product);
        return product;
    }

    private void settingPMInfo(Product product) {
        ProductManager productManager = productManagerService.getByProductId(product.getId());
        product.setCapitalName(productManager.getCapitalName());
        product.setCapitalLogo(productManager.getCapitalLogo());
    }

    @Override
    public ProductVO detail(Long id) {
        ProductVO productVO = OrderFinancingGoodsWrapper.build().entityProductVO(getById(id));
        settingPMInfo(productVO);
        final String tenantId = UserUtils.getTenantId();
        productConfigInfoHandlers.parallelStream().forEach(e -> {
            TenantBroker.runAs(tenantId, a -> e.assembleProduct(productVO));
        });
        return productVO;
    }

    @Override
    public ProductVO detailByConfigType(Long id, List<Integer> configType) {
        ProductVO productVO = OrderFinancingGoodsWrapper.build().entityProductVO(getById(id));
        settingPMInfo(productVO);
        productConfigInfoHandlers.parallelStream().forEach(configInfoHandler -> TenantBroker.runAs(AuthUtil.getTenantId(), tenant -> {
            if (configType.contains(configInfoHandler.support())) {
                configInfoHandler.assembleProduct(productVO);
            }
        }));
        return productVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductDTO saveGoods(ProductDTO productDTO) {
        saveBaseProduct(productDTO);
        productConfigInfoHandlers.forEach(e -> e.saveWithProduct(productDTO));
        return productDTO;
    }

    /**
     * 保存产品信息
     *
     * @param productDTO
     */
    @Override
    public boolean saveBaseProduct(ProductDTO productDTO) {
        //检查
        checkGoodsNameIsRepeat(productDTO.getId(), productDTO.getGoodsName());
        //保存产品
        if (StringUtil.isBlank(productDTO.getGoodsCode())) {
            productDTO.setGoodsCode(CodeUtil.generateCode(CodeEnum.GOODS_CODE));
        }
        OrderFinancingGoods goods = BeanUtil.copyProperties(productDTO, OrderFinancingGoods.class);
        saveOrUpdate(goods);
        productDTO.setId(goods.getId());
        //产品管理保存
        saveOrUpdateProductManager(goods);
        return true;
    }

    private void saveOrUpdateProductManager(OrderFinancingGoods goods) {
        //获取资方信息
        Dept capitalInfo = deptService.getDeptById(goods.getCapitalId()).getData();
        ProductManager productManager = ProductManager.builder()
                .productId(goods.getId())
                .type(goods.getType())
                .goodsName(goods.getGoodsName())
                .capitalName(capitalInfo.getDeptName())
                .capitalLogo(capitalInfo.getLogoSrc())
                .build();
        productManager.setStatus(goods.getStatus());
        productManagerService.saveOrUpdateProductManager(productManager);
    }

    private void checkGoodsNameIsRepeat(Long goodsId, String goodsName) {
        LambdaQueryWrapper<OrderFinancingGoods> wrapper = Wrappers.<OrderFinancingGoods>lambdaQuery().eq(OrderFinancingGoods::getGoodsName, goodsName.trim())
                .eq(OrderFinancingGoods::getStatus, GoodsEnum.ON_SHELF.getCode())
                .ne(Objects.nonNull(goodsId), OrderFinancingGoods::getId, goodsId);
        Integer count = baseMapper.selectCount(wrapper);
        Assert.isFalse(count > 0, "产品名称不能重复");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductDTO updateGoods(ProductDTO productDTO) {
        //更新产品
        OrderFinancingGoods goods = BeanUtil.copyProperties(productDTO, OrderFinancingGoods.class);
        checkGoodsNameIsRepeat(productDTO.getId(), productDTO.getGoodsName());
        updateById(goods);
        //更新管理表
        saveOrUpdateProductManager(goods);
        productConfigInfoHandlers.forEach(e -> e.updateWithProduct(productDTO));
        return productDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> onShelf(Long id) {
        //上架
        List<Long> ids = Collections.singletonList(id);
        changeStatus(ids, GoodsEnum.ON_SHELF.getCode());
        productConfigInfoHandlers.forEach(e -> e.onShelfWithProduct(ids));
        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> offShelf(List<Long> ids) {
        changeStatus(ids, GoodsEnum.OFF_SHELF.getCode());
        productConfigInfoHandlers.forEach(e -> e.offShelfWithProduct(ids));
        //上架
        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchOnShelf(List<Long> ids) {
        //上架
        changeStatus(ids, GoodsEnum.ON_SHELF.getCode());
        productConfigInfoHandlers.forEach(e -> e.onShelfWithProduct(ids));
        return ids;
    }

    @Override
    public List<ProductVO> selectHighQualityGoodsList() {
        return leftJoinProductManager(new MPJLambdaWrapper<>()).eq(OrderFinancingGoods::getIsHighQuality, GoodsEnum.IS_HIGH_QUALITY.getCode())
                .eq(OrderFinancingGoods::getStatus, GoodsEnum.ON_SHELF.getCode())
                .last("limit 6")
                .orderByDesc(OrderFinancingGoods::getCreateTime).list(ProductVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyById(Long goodsId) {
        //产品基础信息复制保存
        ProductVO detailVO = BeanUtil.copyProperties(detail(goodsId), ProductVO.class, "goodsCode", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
        detailVO.setStatus(CommonConstant.OFF_RELEASE);
        detailVO.setIsHighQuality(GoodsEnum.IS_NO_HIGH_QUALITY.getCode());
        detailVO.setGoodsName(detailVO.getGoodsName().concat("copy").concat(RandomUtil.randomNumbers(3)));
        ProductDTO productDTO = entityProductDTO(detailVO);
        saveBaseProduct(productDTO);
        //产品配置信息复制保存
        productConfigInfoHandlers.forEach(e -> {
            e.copyWithGoods(productDTO, goodsId);
        });
    }

    @Override
    public Integer support() {
        return GoodsTypeEnum.ORDER_FINANCING.getCode();
    }

    /**
     * 产品分类删除时触发
     *
     * @param goodsTypeIds
     */
    @Override
    public void deleteWithGoodsType(List<Long> goodsTypeIds) {
        int count = count(Wrappers.<OrderFinancingGoods>lambdaQuery().eq(OrderFinancingGoods::getGoodsTypeId, goodsTypeIds));
        if (count > 0) {
            throw new ServiceException("请先删除相关产品");
        }
    }

    @Override
    public void setIsHighQuality(Long id, Integer isHighQuality) {
        update(Wrappers.<OrderFinancingGoods>lambdaUpdate().eq(OrderFinancingGoods::getId, id).set(OrderFinancingGoods::getIsHighQuality, isHighQuality));
    }

    @Override
    public List<OrderFinancingGoodsVo> getGoodsList() {
        //状态2 已上架
        List<OrderFinancingGoods> goods = baseMapper.selectList(Wrappers.<OrderFinancingGoods>lambdaQuery().eq(BaseEntity::getStatus, GoodsEnum.ON_SHELF.getCode()).eq(OrderFinancingGoods::getType,GoodsEnum.ORDER_FINANCING.getCode()));
        if (CollUtil.isEmpty(goods)) {
            return Collections.emptyList();
        }
        List<OrderFinancingGoodsVo> goodsVOS = goods.stream().map(good -> {
            OrderFinancingGoodsVo goodsVO = new OrderFinancingGoodsVo();
            goodsVO.setId(good.getId());
            goodsVO.setGoodsName(good.getGoodsName());
            return goodsVO;
        }).collect(Collectors.toList());
        return goodsVOS;
    }

    //	private List<GoodsVO> selectGoodsPage(Long companyId){
//		List<Goods> goods = baseMapper.selectList(Wrappers.<Goods>lambdaQuery().eq(Goods::getCapitalId,companyId));
//		List<GoodsVO> goodsVOList = GoodsWrapper.build().listVO(goods);
//		if (CollectionUtil.isEmpty(goodsVOList)){
//			return Collections.emptyList();
//		}
//		// 从产品列表中获取用户id，并查询用户
//		Map<Long, User> userMap = StreamUtil.toMap(userService.listByIds(StreamUtil.map(goodsVOList, Goods::getUpdateUser)), User::getId, user -> user);
//		// 查询资方信息
//		Map<Long, Dept> deptMap = deptService.getMapInId(StreamUtil.map(goodsVOList, Goods::getCapitalId));
//		// 组装数据
//		goodsVOList = goodsVOList.stream().peek(goodsVO -> {
//			// 设置资金方logo和名称
//			Dept dept = deptMap.get(goodsVO.getCapitalId());
//			if (Objects.nonNull(dept)) {
//				goodsVO.setCapitalLogo(dept.getLogoSrc());
//				goodsVO.setCapitalName(dept.getDeptName());
//			}
//			// 设置操作人名称
//			User user = userMap.get(goodsVO.getUpdateUser());
//			goodsVO.setOperator(Objects.isNull(user) ? "" : user.getAccount());
//		}).collect(Collectors.toList());
//		return goodsVOList;
//	}
//
//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public Long saveGoods(GoodsDTO goods) {
//		// 检查产品名称是否重复
//		checkGoodsNameIsRepeat(goods);
//		// 保存产品
//		goods.setGoodsCode(CodeUtil.generateCode(CodeEnum.GOODS_CODE));
//		save(goods);
//		// 保存产品和标签的关联关系
//		submitGoods(goods);
//		return goods.getId();
//	}
//
//	/**
//	 * 根据产品id获取资方对应的合利宝账户-子商户编号
//	 *
//	 * @param goodsId
//	 * @return
//	 */
//	@Override
//	public String goodsMerchantNo(Long goodsId) {
//		Long capitalId = baseMapper.selectCapitalId(goodsId).getCapitalId();
//		List<Long> companyDeptId = deptService.getCompanyDeptId(capitalId.toString(), DeptCommonConstant.DeptCapitalTypeEnum.CAPITAL_TYPE_MISTAKE_BANK.getCode());
//		if (CollUtil.isEmpty(companyDeptId)) {
//			return null;
//		}
//		//根据机构id获取 商户信息
//		MergePay mergePay = payCombineMergeMapper.selectOne(Wrappers.<MergePay>lambdaQuery().in(MergePay::getDeptId, companyDeptId).last("limit 1"));
//		return mergePay.getMerchantNo();
//	}
//
//	private void submitGoods(GoodsDTO goods) {
//		//构建产品费用产品账户参数
//		buildCloudProductParameter(goods);
//		Long id = goods.getId();
//		goodsLabelRelationService.saveGoodsLabelRelation(id, goods.getGoodsLabelIds());
//		// 保存产品开通流程
//		goodsOpeningProcessService.saveGoodsOpeningProcess(id, goods.getGoodsOpeningProcesses());
//		// 保存产品常见问题
//		goodsQuestionService.saveGoodsQuestion(id, goods.getGoodsQuestions());
//
//		//goodsExpenseRelationService.saveOrUpdateGoodsExpenseRelation(id, goods.getCapitalExpenses(), goods.getPlatformExpenses());
//		//billBankCardaRelationService.saveOrUpdateGoodsAccount(id, goods.getCapitalBillBankCardas(), goods.getPlatformBillBankCardas());
//		// 保存产品费用
//		goodsExpenseRelationService.saveOrUpdateGoodsExpenseRelation(id,goods.getGoodsExpenseRelations());
//		//保存产品账户关联
//		expenseAccountRelationService.saveExpenseAccountRelation(id,goods.getExpenseAccountRelations());
//		//保存产品账户
//		billBankCardaRelationService.saveGoodsAccount(id, goods.getBillBankCardaRelation());
//		// 保存产品资料
//		goodsMaterialService.saveOrUpdateGoodsMaterial(id, goods.getGoodsMaterials());
//		// 保存产品合同模板
//		goodsContractTemplateService.submitGoodsContractTemplate(id, goods.getGoodsContractTemplates());
//		// 保存产品绑定流程
//		goodsProcessService.saveGoodsProcess(goods.getGoodsProcessList(), id);
//		// 保存产品定时任务
//		goodsTimingService.saveGoodsTiming(goods.getGoodsTimingList(), id);
//	}
//
//	/**
//	 * 构建产品费用产品账户参数
//	 * @param goods
//	 */
//	private void buildCloudProductParameter(GoodsDTO goods) {
//		List<GoodsExpenseBankVO> goodsExpense = goods.getGoodsExpense();
//		//产品费用参数
//		List<GoodsExpenseRelation> goodsExpenseRelationList = Lists.newArrayList();
//		//产品账户参数
//		List<BillBankCardaRelation> billBankCardaRelationList = Lists.newArrayList();
//		//产品费用账户关联
//		MergePay mergePay = SpringUtil.getBean(IPayCombineMergeService.class).getOne(Wrappers.<MergePay>lambdaQuery().eq(MergePay::getDeptId, AuthUtil.getDeptId()));
//		List<ExpenseAccountRelation> expenseAccountRelations = Lists.newArrayList();
//		if (!CollectionUtil.isEmpty(goodsExpense)){
//			for (GoodsExpenseBankVO goodsExpenseBankVO : goodsExpense) {
//				ExpenseAccountRelation expenseAccountRelation = new ExpenseAccountRelation();
//				List<GoodsExpenseRelation> goodsExpenseRelations = goodsExpenseBankVO.getGoodsExpenseRelations();
//				BillBankCardaRelation billBankCardaRelation = goodsExpenseBankVO.getBillBankCardaRelation();
//				if (ObjectUtil.isNotEmpty(billBankCardaRelation) && Objects.nonNull(billBankCardaRelation.getBillBankCardaId()) || Objects.nonNull(billBankCardaRelation.getPlatformCostPayMode())){
//					if (GoodsEnum.GOODS_TYPE_PLATFORM.getCode().equals(billBankCardaRelation.getExpenseKey()) && billBankCardaRelation.getPlatformCostPayMode().equals(PayModeEnum.PAY_MODE_ONLINE.getCode())){
//						billBankCardaRelation.setDeptId(AuthUtil.getDeptId());
//						billBankCardaRelation.setBillBankCardaId(Objects.nonNull(mergePay)?mergePay.getId():null);
//					}
//					billBankCardaRelationList.add(billBankCardaRelation);
//				}
//				goodsExpenseRelations.forEach(goodsExpenseRelation -> {
//					goodsExpenseRelation.setType(goodsExpenseBankVO.getExpenseKey());
//				});
//				//设置产品费用账户关联表参数
//				expenseAccountRelation.setExpenseId(goodsExpenseBankVO.getExpenseId());
//				expenseAccountRelation.setExpenseKey(goodsExpenseBankVO.getExpenseKey());
//				expenseAccountRelation.setGoodsId(goods.getId());
//				expenseAccountRelation.setEnterpriseType(goodsExpenseBankVO.getEnterpriseType());
//				expenseAccountRelation.setExpenseName(goodsExpenseBankVO.getExpenseName());
//				expenseAccountRelations.add(expenseAccountRelation);
//				goodsExpenseRelationList.addAll(goodsExpenseRelations);
//			}
//		}
//		goods.setGoodsExpenseRelations(goodsExpenseRelationList);
//		goods.setBillBankCardaRelation(billBankCardaRelationList);
//		goods.setExpenseAccountRelations(expenseAccountRelations);
//	}
//
//	private void checkGoodsNameIsRepeat(Goods goods) {
//		Long id = goods.getId();
//		LambdaQueryWrapper<Goods> wrapper = Wrappers.<Goods>lambdaQuery().eq(Goods::getGoodsName, goods.getGoodsName().trim())
//			.eq(Goods::getStatus, GoodsEnum.ON_SHELF.getCode())
//			.ne(Objects.nonNull(id), Goods::getId, id);
//		Integer count = baseMapper.selectCount(wrapper);
//		Assert.isFalse(count > 0, "产品名称不能重复");
//	}
//
//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public boolean updateGoods(GoodsDTO goods) {
//		// 检查产品是否上架
//		Long id = goods.getId();
//		checkGoodsIsOnShelf(id);
//		submitGoods(goods);
//		return updateById(goods);
//	}
//
//	@Override
//	public boolean updateGoodsRiskControl(Goods goods) {
//		// 检查产品是否上架
//		checkGoodsIsOnShelf(goods.getId());
//		return updateById(goods);
//	}
//
//	@Override
//	public GoodsVO detail(Long id) {
//		Goods goods = baseMapper.selectById(id);
//		if (Objects.isNull(goods)) {
//			return null;
//		}
//		GoodsVO goodsVO = GoodsWrapper.build().entityVO(goods);
//		// 查询产品标签
//		CompletableFuture<Map<Long, List<LabelVO>>> labelMapFuture = ThreadUtils.supplyAsync(() -> goodsLabelRelationService.getLabelMapInGoodsId(Lists.newArrayList(id)));
//		// 查询常见问题
//		CompletableFuture<List<GoodsQuestion>> goodsQuestionFuture = ThreadUtils.supplyAsync(() -> goodsQuestionService.selectGoodsQuestionListByGoodsId(id));
//		// 查询产品开通流程
//		CompletableFuture<List<GoodsOpeningProcess>> goodsOpeningProcessFuture = ThreadUtils.supplyAsync(() -> goodsOpeningProcessService.selectGoodsOpeningProcessListByGoodsId(id));
//		// 查询定时任务
//		CompletableFuture<List<GoodsTiming>> goodsTimingFuture = ThreadUtils.supplyAsync(() -> goodsTimingService.selectGoodsTiming(goodsVO.getId()));
//
//		// 阻塞等待执行完成
//		List<?> list = ThreadUtils.allOf(labelMapFuture, goodsQuestionFuture, goodsOpeningProcessFuture, goodsTimingFuture).join();
//		// 设置参数
//		Map<Long, List<LabelVO>> labelMap = (Map<Long, List<LabelVO>>) list.get(0);
//
//		List<LabelVO> labelVOList = labelMap.getOrDefault(id, Lists.newArrayList());
//		goodsVO.setLabelList(labelVOList);
//		goodsVO.setLabelIds(labelVOList.parallelStream().map(LabelVO::getId).collect(Collectors.toList()));
//		goodsVO.setGoodsQuestions((List<GoodsQuestion>) list.get(1));
//		goodsVO.setGoodsOpeningProcesses((List<GoodsOpeningProcess>) list.get(2));
//		goodsVO.setGoodsTimingList((List<GoodsTiming>) list.get(3));
//		return goodsVO;
//	}
//
//
//	@Override
//	public boolean onShelf(Long id) {
//		String tenantId = AuthUtil.getTenantId();
//		// 查询产品信息
//		CompletableFuture<Goods> goodsFuture = ThreadUtils.supplyAsync(() -> baseMapper.selectById(id));
//		// 查询产品资料信息
//		CompletableFuture<List<GoodsMaterial>> goodsMaterialFuture = ThreadUtils.supplyAsync(() -> goodsMaterialService.selectGoodsMaterialListByGoodsId(id));
//		// 查询产品费用信息
//		CompletableFuture<List<GoodsExpenseRelation>> goodsExpenseFuture = ThreadUtils.supplyAsync(() -> goodsExpenseRelationService.selectGoodsExpenseRelationListByGoodsId(id));
//		// 查询合同模板
//		CompletableFuture<List<GoodsContractTemplateVO>> goodsContractTemplateFuture = ThreadUtils.supplyAsync(() -> TenantBroker.applyAs(tenantId, e -> goodsContractTemplateService.selectGoodsContractTemplateListByGoodsId(id)));
//		// 查询产品开通流程
//		CompletableFuture<List<GoodsOpeningProcess>> goodsOpeningProcessFuture = ThreadUtils.supplyAsync(() -> goodsOpeningProcessService.selectGoodsOpeningProcessListByGoodsId(id));
//		// 查询产品常见问题
//		CompletableFuture<List<GoodsQuestion>> goodsQuestionFuture = ThreadUtils.supplyAsync(() -> goodsQuestionService.selectGoodsQuestionListByGoodsId(id));
//		// 查询产品绑定流程
//		CompletableFuture<List<GoodsProcess>> goodsProcessFuture = ThreadUtils.supplyAsync(() -> goodsProcessService.listByGoodsId(id));
//		// 查询定时任务
//		CompletableFuture<List<GoodsTiming>> goodsTimingFuture = ThreadUtils.supplyAsync(() -> goodsTimingService.selectGoodsTiming(id));
//
//		// 阻塞等待执行完成
//		List<?> list = ThreadUtils.allOf(goodsFuture, goodsMaterialFuture, goodsExpenseFuture, goodsContractTemplateFuture, goodsOpeningProcessFuture, goodsQuestionFuture, goodsProcessFuture, goodsTimingFuture).join();
//		Goods goods = (Goods) list.get(0);
//		// 检查是否可操作
//		if (canOperator() && !CommonConstant.OFF_RELEASE.equals(goods.getStatus())) {
//			throw new ServiceException("仅未上架状态下才能进行上架操作");
//		}
//		// 检查产品名称是否重复
//		checkGoodsNameIsRepeat(goods);
//		List<GoodsMaterial> goodsMaterials = (List<GoodsMaterial>) list.get(1);
//		List<GoodsExpenseRelation> goodsExpenseRelations = (List<GoodsExpenseRelation>) list.get(2);
//		List<GoodsContractTemplateVO> goodsContractTemplates = (List<GoodsContractTemplateVO>) list.get(3);
//		List<GoodsOpeningProcess> goodsOpeningProcesses = (List<GoodsOpeningProcess>) list.get(4);
//		List<GoodsQuestion> goodsQuestions = (List<GoodsQuestion>) list.get(5);
//		List<GoodsProcess> goodsProcessList = (List<GoodsProcess>) list.get(6);
//		List<GoodsTiming> goodsTimingList = (List<GoodsTiming>) list.get(7);
//
//		if (CollUtil.isEmpty(goodsTimingList) || goodsTimingList.size() < 2) {
//			throw new ServiceException("请将定时任务的信息填写完成,再上架");
//		}
//		//检查合同是否配置正确
//		checkContractTemplate(goodsContractTemplates);
////		//校验参数是否为空
//		if (verifyParameterGoodsIsComplete(goods, goodsMaterials, goodsExpenseRelations, goodsContractTemplates, goodsOpeningProcesses, goodsQuestions, goodsProcessList)) {
//			throw new ServiceException("请将6个页面的信息填写完成,再上架");
//		}
//		// 检查是否为空
////		if (checkGoodsIsComplete(goods, goodsMaterials, goodsExpenseRelations, goodsContractTemplates, goodsOpeningProcesses, goodsQuestions, goodsProcessList)) {
////			throw new ServiceException("请将6个页面的信息填写完成,再上架");
////		}
//		// 检查产品部分参数是否填写正确
//		checkGoodsParamIsCorrect(goods);
//		// 检查收费方式是否填写正确
//		Assert.isFalse(checkChargeMethod(goods.getChargeMethod(), goodsExpenseRelations), "自动放款不允许添加需手填的费用");
//		// 更新状态
//		goods.setStatus(GoodsEnum.ON_SHELF.getCode());
//
////		//发送消息 TODO
//		MessageSendDto messageSend = new MessageSendDto();
//		messageSend.setTemplateCode("J091424493047013");
//		messageSend.setUserId(goods.getCreateUser().toString());
//		HashMap<String, Object> map = MapUtil.newHashMap();
//		messageSend.setParam(map);
//		messageSendService.sendMessage(messageSend);
//		return updateById(goods);
//	}
//
//
//	private void checkContractTemplate(List<GoodsContractTemplateVO> goodsContractTemplates) {
//		if (CollectionUtil.isEmpty(goodsContractTemplates)) {
//			return;
//		}
//		Set<String> templateIds = goodsContractTemplates.stream().map(GoodsContractTemplate::getContractTemplateId).collect(Collectors.toSet());
//		Map<String, List<ContractSignConfig>> templateSignConfigMap = contractSignConfigService.mapByTemplateIds(new ArrayList<>(templateIds));
//		for (GoodsContractTemplateVO goodsContractTemplate : goodsContractTemplates) {
//			ContractTemplate contractTemplate = goodsContractTemplate.getContractTemplate();
//			String signNode = contractTemplate.getSignNode();
//			String goodsSignNodes = goodsContractTemplate.getSignNode();
//			if (StringUtil.isBlank(goodsSignNodes)) {
//				throw new ServiceException("请将合同签署节点补充完毕");
//			}
//			for (String node : Func.toStrList(goodsSignNodes)) {
//				if (!signNode.contains(node)) {
//					throw new ServiceException(String.format("合同模板[ %s ]签署节点设置错误", contractTemplate.getTemplateName()));
//				}
//			}
//			if (!templateSignConfigMap.containsKey(contractTemplate.getTemplateId())) {
//				throw new ServiceException(String.format("合同模板[ %s ]签署关键字未配置", contractTemplate.getTemplateName()));
//			}
//		}
//	}
//
//
//	private boolean checkChargeMethod(Integer chargeMethod, List<GoodsExpenseRelation> goodsExpenseRelations) {
//		if (chargeMethod.equals(GoodsEnum.AUTO_LENDING.getCode())) {
//			List<Integer> calculate = StreamUtil.map(goodsExpenseRelations, GoodsExpenseRelation::getCalculation);
//			return calculate.contains(GoodsConstant.CALCULATION_ONE);
//		}
//		return false;
//	}
//
//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public boolean offShelf(List<Long> ids) {
//		// 下架轮播图
//		rotationService.update(Wrappers.<Rotation>lambdaUpdate().in(Rotation::getGoodsId, ids).set(Rotation::getStatus, 0));
//
//		SpringUtil.getBean(IQualityProductsService.class).update(Wrappers.<QualityProducts>lambdaUpdate().in(QualityProducts::getProductsId, ids).set(BaseEntity::getStatus, 1));
//		// 下架产品
//
//		List<Goods> goodsList = goodsMapper.selectBatchIds(ids);
//		goodsList.forEach(e -> {
//			//下架检查
//			if (!GoodsEnum.ON_SHELF.getCode().equals(e.getStatus())) {
//				throw new ServiceException("下架失败,产品未上架");
//			}
//			//发送消息 TODO
//			MessageSendDto messageSend = new MessageSendDto();
//			messageSend.setTemplateCode("J121596473547863");
//			messageSend.setUserId(e.getCreateUser().toString());
//			HashMap<String, Object> map = MapUtil.newHashMap();
//			messageSend.setParam(map);
//			messageSendService.sendMessage(messageSend);
//		});
//		//将所有相关客户产品设为禁用
//		ICustomerGoodsService customerGoodsService = SpringUtil.getBean(ICustomerGoodsService.class);
//		IEnterpriseQuotaService quotaService = SpringUtil.getBean(IEnterpriseQuotaService.class);
//		List<CustomerGoods> list = customerGoodsService.list(Wrappers.<CustomerGoods>lambdaQuery().in(CustomerGoods::getGoodsId, ids));
//		if (CollUtil.isNotEmpty(list)) {
//			List<Long> enterpriseQuotaId = list.stream().map(CustomerGoods::getEnterpriseQuotaId).collect(Collectors.toList());
//			customerGoodsService.update(Wrappers.<CustomerGoods>lambdaUpdate()
//				.in(CustomerGoods::getGoodsId, ids).set(CustomerGoods::getStatus, CustomerGoodsEnum.DISABLE.getCode()));
//			quotaService.update(Wrappers.<EnterpriseQuota>lambdaUpdate().in(EnterpriseQuota::getId, enterpriseQuotaId)
//				.set(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.DISABLE.getCode())
//				.set(EnterpriseQuota::getDisableReason, "产品已永久下架，请关注最新产品进行开通"));
//		}
//		return changeStatus(ids, GoodsEnum.OFF_SHELF.getCode());
//	}
//
//	@Override
//	public Integer batchOnShelf(List<Long> ids) {
//		// 查询产品信息
//		CompletableFuture<List<Goods>> goodsFuture = ThreadUtils.supplyAsync(() -> baseMapper.selectBatchIds(ids));
//		// 查询产品资料信息
//		CompletableFuture<Map<Long, List<GoodsMaterial>>> goodsMaterialFuture = ThreadUtils.supplyAsync(() -> goodsMaterialService.selectGoodsMaterialMapInGoodsId(ids));
//		// 查询产品费用信息
//		CompletableFuture<Map<Long, List<GoodsExpenseRelation>>> goodsExpenseFuture = ThreadUtils.supplyAsync(() -> goodsExpenseRelationService.selectGoodsExpenseRelationMapInGoodsId(ids));
//		// 查询合同模板
//		CompletableFuture<Map<Long, List<GoodsContractTemplateVO>>> goodsContractTemplateFuture = ThreadUtils.supplyAsync(() -> goodsContractTemplateService.selectGoodsContractTemplateMapInGoodsId(ids));
//		// 查询产品开通流程
//		CompletableFuture<Map<Long, List<GoodsOpeningProcess>>> goodsOpeningProcessFuture = ThreadUtils.supplyAsync(() -> goodsOpeningProcessService.selectGoodsOpeningProcessMapInGoodsId(ids));
//		// 查询产品常见问题
//		CompletableFuture<Map<Long, List<GoodsQuestion>>> goodsQuestionFuture = ThreadUtils.supplyAsync(() -> goodsQuestionService.selectGoodsQuestionMapInGoodsId(ids));
//		// 查询产品绑定流程
//		CompletableFuture<Map<Long, List<GoodsProcess>>> goodsProcessFuture = ThreadUtils.supplyAsync(() -> goodsProcessService.listInGoodsId(ids));
//		// 阻塞等待执行完成
//		List<?> list = ThreadUtils.allOf(goodsFuture, goodsMaterialFuture, goodsExpenseFuture, goodsContractTemplateFuture, goodsOpeningProcessFuture, goodsQuestionFuture, goodsProcessFuture).join();
//
//		List<Goods> goodsList = (List<Goods>) list.get(0);
//		Map<Long, List<GoodsMaterial>> goodsMaterialMap = (Map<Long, List<GoodsMaterial>>) list.get(1);
//		Map<Long, List<GoodsExpenseRelation>> goodsExpenseRelationMap = (Map<Long, List<GoodsExpenseRelation>>) list.get(2);
//		Map<Long, List<GoodsContractTemplateVO>> goodsContractTemplateMap = (Map<Long, List<GoodsContractTemplateVO>>) list.get(3);
//		Map<Long, List<GoodsOpeningProcess>> goodsOpeningProcessMap = (Map<Long, List<GoodsOpeningProcess>>) list.get(4);
//		Map<Long, List<GoodsQuestion>> goodsQuestionMap = (Map<Long, List<GoodsQuestion>>) list.get(5);
//		Map<Long, List<GoodsProcess>> goodsProcessMap = (Map<Long, List<GoodsProcess>>) list.get(6);
//		// 过滤信息未填写完整的
//		List<Long> goodsIds = goodsList.parallelStream().filter(goods -> {
//			Long id = goods.getId();
//			List<GoodsMaterial> goodsMaterials = goodsMaterialMap.getOrDefault(id, Lists.newArrayList());
//			List<GoodsExpenseRelation> goodsExpenseRelations = goodsExpenseRelationMap.getOrDefault(id, Lists.newArrayList());
//			List<GoodsContractTemplateVO> goodsContractTemplates = goodsContractTemplateMap.getOrDefault(id, Lists.newArrayList());
//			List<GoodsOpeningProcess> goodsOpeningProcess = goodsOpeningProcessMap.getOrDefault(id, Lists.newArrayList());
//			List<GoodsQuestion> goodsQuestions = goodsQuestionMap.getOrDefault(id, Lists.newArrayList());
//			List<GoodsProcess> goodsProcessList = goodsProcessMap.getOrDefault(id, Collections.emptyList());
//			return !checkGoodsIsComplete(goods, goodsMaterials, goodsExpenseRelations, goodsContractTemplates, goodsOpeningProcess, goodsQuestions, goodsProcessList)
//				&& !checkChargeMethod(goods.getChargeMethod(), goodsExpenseRelations);
//		}).map(Goods::getId).collect(Collectors.toList());
//
//		if (CollectionUtils.isEmpty(goodsIds)) {
//			return 0;
//		}
//		changeStatus(goodsIds, GoodsEnum.ON_SHELF.getCode());
//		return goodsIds.size();
//	}
//
//	@Override
//	public List<GoodsVO> selectHighQualityGoodsList() {
//		List<Goods> goodsList = baseMapper.selectList(Wrappers.<Goods>lambdaQuery()
//			.eq(Goods::getIsHighQuality, GoodsEnum.IS_HIGH_QUALITY.getCode())
//			.eq(Goods::getStatus, GoodsEnum.ON_SHELF.getCode())
//			.last("limit 6")
//			.orderByDesc(Goods::getCreateTime));
//		if (CollectionUtils.isEmpty(goodsList)) {
//			return Lists.newArrayList();
//		}
//		// 查询产品标签和资方
//		return selectGoodsCapitalAndLabel(goodsList);
//
//	}
//
//	private List<GoodsVO> selectGoodsCapitalAndLabel(List<Goods> goodsList) {
//		// 查询产品标签
//		CompletableFuture<Map<Long, List<LabelVO>>> labelMapFuture = ThreadUtils.supplyAsync(() -> goodsLabelRelationService.getLabelMapInGoodsId(StreamUtil.map(goodsList, Goods::getId)));
//		// 查询产品绑定资方
//		CompletableFuture<Map<Long, Dept>> capitalInfoMapFuture = ThreadUtils.supplyAsync(() -> deptService.getMapInId(StreamUtil.map(goodsList, Goods::getCapitalId)));
//		// 阻塞等待执行完成
//		List<?> list = ThreadUtils.allOf(labelMapFuture, capitalInfoMapFuture).join();
//		Map<Long, List<LabelVO>> labelMap = (Map<Long, List<LabelVO>>) list.get(0);
//
//		Map<Long, Dept> capitalInfoMap = (Map<Long, Dept>) list.get(1);
//
//		// 组装数据
//		return goodsList.parallelStream().map(goods -> {
//			GoodsVO goodsVO = GoodsWrapper.build().entityVO(goods);
//			goodsVO.setLabelList(labelMap.get(goods.getId()));
//			Dept dept = capitalInfoMap.get(goods.getCapitalId());
//			if (Objects.nonNull(dept)) {
//				goodsVO.setCapitalLogo(dept.getLogoSrc());
//				goodsVO.setCapitalName(dept.getDeptName());
//			}
//			return goodsVO;
//		}).collect(Collectors.toList());
//	}
//
//	@Override
//	public GoodsVO selectDetailById(Long id) {
//		GoodsVO goodsVO = detail(id);
//		if (Objects.isNull(goodsVO)) {
//			return null;
//		}
//		Dept dept = deptService.getById(goodsVO.getCapitalId());
//		if (Objects.nonNull(dept)) {
//			goodsVO.setCapitalName(dept.getDeptName());
//			goodsVO.setCapitalLogo(dept.getLogoSrc());
//		}
//		if (Objects.nonNull(MyAuthUtil.getUserId())) {
//			ICustomerGoodsService customerGoodsService = SpringUtil.getBean(ICustomerGoodsService.class);
//			Integer financingCode = CustomerGoodsEnum.FINANCING.getCode();
//			Integer expireCode = CustomerGoodsEnum.EXPIRE.getCode();
//			Integer disableCode = CustomerGoodsEnum.DISABLE.getCode();
//			Integer quotaChangeCode = CustomerGoodsEnum.QUOTA_CHANGE.getCode();
//			List<CustomerGoods> customerGoodsList = customerGoodsService.lambdaQuery()
//				.in(CustomerGoods::getStatus, Lists.newArrayList(financingCode, expireCode, disableCode, quotaChangeCode))
//				.eq(CustomerGoods::getEnterpriseId, MyAuthUtil.getUserId())
//				.eq(CustomerGoods::getGoodsId, id)
//				.orderByDesc(CustomerGoods::getCreateTime)
//				.list();
//			if (!CollectionUtils.isEmpty(customerGoodsList)) {
//				CustomerGoods customerGoods = customerGoodsList.get(0);
//				goodsVO.setCustomerGoodsId(customerGoods.getId());
//				goodsVO.setEnterpriseQuotaId(customerGoods.getEnterpriseQuotaId());
//				goodsVO.setStatus(customerGoods.getStatus());
//			}
//		}
//		return goodsVO;
//	}
//
//	@Override
//	public IPage<GoodsVO> selectClientGoodsList(GoodsSearchDTO goodsSearchDTO, Query query) {
//		IPage<Goods> page = baseMapper.selectPage(Condition.getPage(query), buildQueryWrapper(goodsSearchDTO));
//		if (page.getTotal() <= 0) {
//			return null;
//		}
//		Page<GoodsVO> pageVO = GoodsWrapper.build().pageVO(page);
//		List<GoodsVO> goodsVOList = pageVO.getRecords();
//		List<Long> goodsIdList = StreamUtil.map(goodsVOList, Goods::getId);
//		List<Long> capitalIdList = StreamUtil.map(goodsVOList, Goods::getCapitalId);
//		//获取当前用户关联的核心企业已生效的产品 k:产品id v:产品id
//		Map<String, String> relationCoreGoods = new HashMap<>();
//		final Integer label = MyAuthUtil.getLabel();
//		if (MyAuthUtil.isLogin()) {
//			Long userId = MyAuthUtil.getUserId();
//			ICustomerGoodsService customerGoodsService = SpringUtil.getBean(ICustomerGoodsService.class);
//			//查询关联的核心企业所有生效产品
//			relationCoreGoods = filterOpenBack(userId, goodsIdList);
//			Integer enterpriseType = goodsSearchDTO.getEnterpriseType();
//			List<CustomerGoods> customerGoodsList = customerGoodsService.lambdaQuery()
//				.in(CustomerGoods::getGoodsId, goodsIdList)
//				.eq(CustomerGoods::getEnterpriseId, MyAuthUtil.getUserId())
//				.eq(ObjectUtil.isNotEmpty(enterpriseType), CustomerGoods::getEnterpriseType, enterpriseType)
//				.list();
//			Map<Long, CustomerGoods> customerGoodsMap = StreamUtil.toMap(customerGoodsList, CustomerGoods::getGoodsId, e -> e);
//			final Map<String, String> finalRelationCoreGoods = relationCoreGoods;
//			goodsVOList.forEach(goodsVO -> {
//				Long id = goodsVO.getId();
//				if (customerGoodsMap.containsKey(id)) {
//					CustomerGoods customerGoods = customerGoodsMap.get(id);
//					goodsVO.setCustomerGoodsId(customerGoods.getId());
//					goodsVO.setEnterpriseQuotaId(customerGoods.getEnterpriseQuotaId());
//					goodsVO.setStatus(customerGoods.getStatus());
//				}
//				goodsVO.setCanOpenStatus(showCanOpenGoods(goodsVO, finalRelationCoreGoods, label));
//			});
//		}
//		Integer searchType = goodsSearchDTO.getSearchType();
//		long count = 0;
//		//排除已经开通的 或者 关联核心企业未开通的产品
//		if (Objects.nonNull(searchType) && searchType == 1) {
//			final Map<String, String> finalRelationCoreGoods = relationCoreGoods;
//			count = StreamUtil.filterToStream(goodsVOList, goodsVO -> showCanOpenGoods(goodsVO, finalRelationCoreGoods, label)).count();
//			goodsVOList.removeIf(goodsVO -> showCanOpenGoods(goodsVO, finalRelationCoreGoods, label));
//		}
//		if (!CollectionUtils.isEmpty(goodsVOList)) {
//			Map<Long, List<LabelVO>> labelMap = goodsLabelRelationService.getLabelMapInGoodsId(goodsIdList);
//			Map<Long, Dept> deptMap = deptService.getMapInId(capitalIdList);
//			goodsVOList.forEach(goodsVO -> {
//				goodsVO.setLabelList(labelMap.get(goodsVO.getId()));
//				Dept dept = deptMap.get(goodsVO.getCapitalId());
//				if (Objects.nonNull(dept)) {
//					goodsVO.setCapitalLogo(dept.getLogoSrc());
//				}
//			});
//
//		}
//		pageVO.setTotal(pageVO.getTotal() - count);
//		pageVO.setRecords(goodsVOList);
//		return pageVO;
//	}
//
//	/**
//	 * 筛选可开通产品背景
//	 *
//	 * @param userId
//	 * @param goodsIdList
//	 * @return k:产品id，v背景id
//	 */
//	private Map<String, String> filterOpenBack(Long userId, List<Long> goodsIdList) {
//		Integer label = MyAuthUtil.getLabel();
//		if (!Objects.isNull(label) && CustomerEnum.LABEL_CORE.getCode() == label) {
//			return new HashMap<>();
//		}
//		ICustomerGoodsTradeBackgroundService customerGoodsTradeBackgroundService = SpringUtil.getBean(ICustomerGoodsTradeBackgroundService.class);
//		ITradeBackgroundService backgroundService = SpringUtil.getBean(ITradeBackgroundService.class);
//		IEnterpriseQuotaService quotaService = SpringUtil.getBean(IEnterpriseQuotaService.class);
//		List<TradeBackgroundVO> tradeBackgrounds = backgroundService.listLowerByCompanyId(userId, 3, 2);
//		List<Long> lowerIds = StreamUtil.map(tradeBackgrounds, TradeBackground::getCompanyLowerId);
//		if (CollUtil.isEmpty(lowerIds)) {
//			return new HashMap<>();
//		}
//		//有效的核心企业额度
//		List<EnterpriseQuota> enterpriseQuotas = quotaService.list(Wrappers.<EnterpriseQuota>lambdaQuery()
//			.in(EnterpriseQuota::getEnterpriseId, lowerIds)
//			.in(EnterpriseQuota::getGoodsId, goodsIdList)
//			.eq(EnterpriseQuota::getEnterpriseType, EnterpriseTypeEnum.CORE_ENTERPRISE.getCode())
//			.eq(EnterpriseQuota::getStatus, EnterpriseQuotaStatusEnum.VALID.getCode()));
//		if (CollUtil.isEmpty(enterpriseQuotas)) {
//			return new HashMap<>();
//		}
//		//去重
//		Map<String, String> goodsMap = new HashMap<>();
//		enterpriseQuotas = enterpriseQuotas.stream().filter(e -> {
//			String goodsId = e.getGoodsId().toString();
//			if (!goodsMap.containsKey(goodsId)) {
//				goodsMap.put(goodsId, goodsId);
//				return true;
//			}
//			return false;
//		}).collect(Collectors.toList());
//		//已使用
//		List<CustomerGoodsTradeBackground> customerGoodsTradeBackgroundList =
//			customerGoodsTradeBackgroundService.list(Wrappers.
//				<CustomerGoodsTradeBackground>lambdaQuery()
//				.eq(CustomerGoodsTradeBackground::getUserId, userId)
//				.in(CustomerGoodsTradeBackground::getTradeBackgroundId, StreamUtil.map(tradeBackgrounds, TradeBackground::getId))
//				.ne(CustomerGoodsTradeBackground::getStatus, CustomerGoodsEnum.CLOSING.getCode()));
//		if (CollUtil.isEmpty(customerGoodsTradeBackgroundList)) {
//			return enterpriseQuotas.stream().collect(Collectors.toMap(e -> e.getGoodsId().toString(), e -> e.getGoodsId().toString()));
//		}
//		Map<String, CustomerGoodsTradeBackground> backgroundMap = customerGoodsTradeBackgroundList.stream().collect(Collectors.toMap(e -> e.getTradeBackgroundId().toString(), e -> e));
//		//未使用
//		List<TradeBackgroundVO> unUsedTrade = tradeBackgrounds.stream().filter(e -> !backgroundMap.containsKey(e.getId().toString())).collect(Collectors.toList());
//		Map<String, TradeBackgroundVO> unUsedUsedMap = StreamUtil.toMap(unUsedTrade, e -> e.getCompanyLowerId().toString(), e -> e);
//		//筛选未使用的贸易背景
//		return enterpriseQuotas.stream().filter(e -> unUsedUsedMap.containsKey(e.getEnterpriseId().toString())).collect(Collectors.toMap(e -> e.getGoodsId().toString(), e -> e.getGoodsId().toString()));
//	}
//
//	/**
//	 * 排除开通产品条件 客户已开通 || 关联核心企业未开通
//	 *
//	 * @param goodsVO
//	 * @param finalRelationCoreGoods
//	 * @return
//	 */
//	private Boolean showCanOpenGoods(GoodsVO goodsVO, Map<String, String> finalRelationCoreGoods, Integer labelType) {
//		if (!Objects.isNull(labelType) && CustomerEnum.LABEL_CORE.getCode() == labelType) {
//			return !Objects.isNull(goodsVO.getCustomerGoodsId());
//		}
//		return !Objects.isNull(goodsVO.getCustomerGoodsId()) || !finalRelationCoreGoods.containsKey(goodsVO.getId().toString());
//	}
//
//	private LambdaQueryWrapper<Goods> buildQueryWrapper(GoodsSearchDTO goodsSearchDTO) {
//		LambdaQueryWrapper<Goods> wrapper = Wrappers.<Goods>lambdaQuery();
//		wrapper.eq(Goods::getStatus, GoodsEnum.ON_SHELF.getCode());
//		Integer searchType = goodsSearchDTO.getSearchType();
//		if (Objects.nonNull(searchType)) {
//			// 优质产品
//			if (searchType == 2) {
//				wrapper.eq(Goods::getIsHighQuality, GoodsEnum.IS_HIGH_QUALITY.getCode());
//			}
//			// 按低额度升序
//			if (searchType == 3) {
//				wrapper.orderByAsc(Goods::getAnnualInterestRateStart);
//			} else if (searchType == 4) {
//				// 按高额度降序
//				wrapper.orderByDesc(Goods::getLoanAmountEnd);
//			} else {
//				// 默认安装时间倒序
//				wrapper.orderByDesc(Goods::getCreateTime);
//			}
//		}
//		Integer goodsType = goodsSearchDTO.getGoodsType();
//		if (Objects.nonNull(goodsType)) {
//			wrapper.eq(Goods::getType, goodsType);
//		}
//		Integer lendingMethod = goodsSearchDTO.getLendingMethod();
//		if (Objects.nonNull(lendingMethod)) {
//			wrapper.eq(Goods::getLendingMethod, lendingMethod);
//		}
//		Long capitalId = goodsSearchDTO.getCapitalId();
//		if (Objects.nonNull(capitalId)) {
//			wrapper.eq(Goods::getCapitalId, capitalId);
//		}
//		String goodsName = goodsSearchDTO.getGoodsName();
//		if (StringUtil.isNotBlank(goodsName)) {
//			wrapper.like(Goods::getGoodsName, goodsName);
//		}
//		return wrapper;
//	}
//
    @Override
    public OrderFinancingGoodsVo processGoodsInfo(Long id) {
       OrderFinancingGoods goods = baseMapper.selectById(id);
        if (Objects.isNull(goods)) {
            return null;
        }
        OrderFinancingGoodsVo orderFinancingGoodsVo = OrderFinancingGoodsWrapper.build().entityVO(goods);
        Dept dept =
                deptService.getDeptById(goods.getCapitalId()).getData();
        if (Objects.nonNull(dept)) {
            orderFinancingGoodsVo.setCapitalName(dept.getDeptName());
            orderFinancingGoodsVo.setCapitalLogo(dept.getLogoSrc());
        }
        return orderFinancingGoodsVo;
    }

    //
	@Override
	public Map<Long, OrderFinancingGoods> getMapInId(List<Long> goodsIdList) {
		if (CollectionUtils.isEmpty(goodsIdList)) {
			return Collections.emptyMap();
		}
		return baseMapper.selectBatchIds(goodsIdList).stream().collect(Collectors.toMap(OrderFinancingGoods::getId, obj -> obj));
	}
//
//	@Override
//	public void offShelfAndSetFieldIsNull(List<Long> goodsIdList, SFunction<Goods, ?> function) {
//		LambdaUpdateWrapper<Goods> updateWrapper = Wrappers.<Goods>lambdaUpdate()
//			.in(Goods::getId, goodsIdList)
//			.set(Goods::getStatus, GoodsEnum.OFF_SHELF.getCode());
//		if (Objects.nonNull(function)) {
//			updateWrapper.set(function, null);
//		}
//		update(updateWrapper);
//	}
//
//	@Override
//	public IPage<GoodsVO> selectCapitalIdGoodsPage(Map<String, Object> goods, Query query, Long capitalId) {
//		List<GoodsVO> goodsVOIPage = selectGoodsPage(capitalId);
//		if (ObjectUtil.isEmpty(goodsVOIPage)) {
//			return new Page<GoodsVO>();
//		}
////		List<GoodsVO> filter = StreamUtil.filter(goodsVOIPage, goodsVo -> {
////			return capitalId.equals(goodsVo.getCapitalId());
////		});
//		List<GoodsVO> goodsVOList = goodsVOIPage.stream().skip((long) (query.getCurrent() - 1) * (query.getSize())).limit(query.getSize()).collect(Collectors.toList());
//		Page<GoodsVO> GoodsVOPage = new Page<>();
//		GoodsVOPage.setRecords(goodsVOList);
//		GoodsVOPage.setTotal(goodsVOIPage.size());
//		GoodsVOPage.setCurrent(query.getCurrent());
//		GoodsVOPage.setSize(query.getSize());
//
//		return GoodsVOPage;
//	}
//
//	@Override
//	public IPage<GoodsVO> selectCapitalGoods(GoodsSearchDTO goodsSearchDTO, Query query) {
//		IPage<Goods> page = goodsMapper.selectGoodsPage(Condition.getPage(query), goodsSearchDTO);
//		if (page.getTotal() <= 0) {
//			return null;
//		}
//		Page<GoodsVO> pageVO = GoodsWrapper.build().pageVO(page);
//		List<GoodsVO> goodsVOList = pageVO.getRecords();
//		List<Long> goodsIdList = StreamUtil.map(goodsVOList, Goods::getId);
//		List<Long> capitalIdList = StreamUtil.map(goodsVOList, Goods::getCapitalId);
//		//获取当前用户关联的核心企业已生效的产品 k:产品id v:产品id
//		Map<String, String> relationCoreGoods = new HashMap<>();
//		final Integer label = MyAuthUtil.getLabel();
//		if (MyAuthUtil.isLogin()) {
//			Long userId = MyAuthUtil.getUserId();
//			ICustomerGoodsService customerGoodsService = SpringUtil.getBean(ICustomerGoodsService.class);
//			//查询关联的核心企业所有生效产品
//			relationCoreGoods = filterOpenBack(userId, goodsIdList);
//			Integer enterpriseType = goodsSearchDTO.getEnterpriseType();
//			List<CustomerGoods> customerGoodsList = customerGoodsService.lambdaQuery()
//				.in(CustomerGoods::getGoodsId, goodsIdList)
//				.eq(CustomerGoods::getEnterpriseId, MyAuthUtil.getUserId())
//				.eq(ObjectUtil.isNotEmpty(enterpriseType), CustomerGoods::getEnterpriseType, enterpriseType)
//				.list();
//			Map<Long, CustomerGoods> customerGoodsMap = StreamUtil.toMap(customerGoodsList, CustomerGoods::getGoodsId, e -> e);
//			final Map<String, String> finalRelationCoreGoods = relationCoreGoods;
//			goodsVOList.forEach(goodsVO -> {
//				Long id = goodsVO.getId();
//				if (customerGoodsMap.containsKey(id)) {
//					CustomerGoods customerGoods = customerGoodsMap.get(id);
//					goodsVO.setCustomerGoodsId(customerGoods.getId());
//					goodsVO.setEnterpriseQuotaId(customerGoods.getEnterpriseQuotaId());
//					goodsVO.setStatus(customerGoods.getStatus());
//				}
//				goodsVO.setCanOpenStatus(showCanOpenGoods(goodsVO, finalRelationCoreGoods, label));
//			});
//		}
//		Integer searchType = goodsSearchDTO.getSearchType();
//		long count = 0;
//		//排除已经开通的 或者 关联核心企业未开通的产品
//		if (Objects.nonNull(searchType) && searchType == 1) {
//			final Map<String, String> finalRelationCoreGoods = relationCoreGoods;
//			count = StreamUtil.filterToStream(goodsVOList, goodsVO -> showCanOpenGoods(goodsVO, finalRelationCoreGoods, label)).count();
//			goodsVOList.removeIf(goodsVO -> showCanOpenGoods(goodsVO, finalRelationCoreGoods, label));
//		}
//		if (!CollectionUtils.isEmpty(goodsVOList)) {
//			Map<Long, List<LabelVO>> labelMap = goodsLabelRelationService.getLabelMapInGoodsId(goodsIdList);
//			Map<Long, Dept> deptMap = deptService.getMapInId(capitalIdList);
//			goodsVOList.forEach(goodsVO -> {
//				goodsVO.setLabelList(labelMap.get(goodsVO.getId()));
//				Dept dept = deptMap.get(goodsVO.getCapitalId());
//				if (Objects.nonNull(dept)) {
//					goodsVO.setCapitalLogo(dept.getLogoSrc());
//				}
//			});
//
//		}
//		pageVO.setTotal(pageVO.getTotal() - count);
//		pageVO.setRecords(goodsVOList);
//		return pageVO;
//	}
//
//	@Override
//	public IPage<GoodsVO> selectCapitalProduct(GoodsSearchDTO goodsSearchDTO, Query query) {
//		IPage<Goods> page = goodsMapper.selectProductPage(Condition.getPage(query), goodsSearchDTO);
//		if (page.getTotal() <= 0) {
//			return null;
//		}
//		Page<GoodsVO> pageVO = GoodsWrapper.build().pageVO(page);
//		List<GoodsVO> goodsVOList = pageVO.getRecords();
//		List<Long> goodsIdList = StreamUtil.map(goodsVOList, Goods::getId);
//		List<Long> capitalIdList = StreamUtil.map(goodsVOList, Goods::getCapitalId);
//		//获取当前用户关联的核心企业已生效的产品 k:产品id v:产品id
//		Map<String, String> relationCoreGoods = new HashMap<>();
//		final Integer label = MyAuthUtil.getLabel();
//		if (MyAuthUtil.isLogin()) {
//			Long userId = MyAuthUtil.getUserId();
//			ICustomerGoodsService customerGoodsService = SpringUtil.getBean(ICustomerGoodsService.class);
//			//查询关联的核心企业所有生效产品
//			relationCoreGoods = filterOpenBack(userId, goodsIdList);
//			Integer enterpriseType = goodsSearchDTO.getEnterpriseType();
//			List<CustomerGoods> customerGoodsList = customerGoodsService.lambdaQuery()
//				.in(CustomerGoods::getGoodsId, goodsIdList)
//				.eq(CustomerGoods::getEnterpriseId, MyAuthUtil.getUserId())
//				.eq(ObjectUtil.isNotEmpty(enterpriseType), CustomerGoods::getEnterpriseType, enterpriseType)
//				.list();
//			Map<Long, CustomerGoods> customerGoodsMap = StreamUtil.toMap(customerGoodsList, CustomerGoods::getGoodsId, e -> e);
//			final Map<String, String> finalRelationCoreGoods = relationCoreGoods;
//			goodsVOList.forEach(goodsVO -> {
//				Long id = goodsVO.getId();
//				if (customerGoodsMap.containsKey(id)) {
//					CustomerGoods customerGoods = customerGoodsMap.get(id);
//					goodsVO.setCustomerGoodsId(customerGoods.getId());
//					goodsVO.setEnterpriseQuotaId(customerGoods.getEnterpriseQuotaId());
//					goodsVO.setStatus(customerGoods.getStatus());
//				}
//				goodsVO.setCanOpenStatus(showCanOpenGoods(goodsVO, finalRelationCoreGoods, label));
//			});
//		}
//		Integer searchType = goodsSearchDTO.getSearchType();
//		long count = 0;
//		//排除已经开通的 或者 关联核心企业未开通的产品
//		if (Objects.nonNull(searchType) && searchType == 1) {
//			final Map<String, String> finalRelationCoreGoods = relationCoreGoods;
//			count = StreamUtil.filterToStream(goodsVOList, goodsVO -> showCanOpenGoods(goodsVO, finalRelationCoreGoods, label)).count();
//			goodsVOList.removeIf(goodsVO -> showCanOpenGoods(goodsVO, finalRelationCoreGoods, label));
//		}
//		if (!CollectionUtils.isEmpty(goodsVOList)) {
//			Map<Long, List<LabelVO>> labelMap = goodsLabelRelationService.getLabelMapInGoodsId(goodsIdList);
//			Map<Long, Dept> deptMap = deptService.getMapInId(capitalIdList);
//			goodsVOList.forEach(goodsVO -> {
//				goodsVO.setLabelList(labelMap.get(goodsVO.getId()));
//				Dept dept = deptMap.get(goodsVO.getCapitalId());
//				if (Objects.nonNull(dept)) {
//					goodsVO.setCapitalLogo(dept.getLogoSrc());
//				}
//			});
//
//		}
//		pageVO.setTotal(pageVO.getTotal() - count);
//		pageVO.setRecords(goodsVOList);
//		return pageVO;
//	}
//
//	private boolean verifyParameterGoodsIsComplete(Goods goods, List<GoodsMaterial> goodsMaterials, List<GoodsExpenseRelation> goodsExpenseRelations, List<GoodsContractTemplateVO> goodsContractTemplates, List<GoodsOpeningProcess> goodsOpeningProcesses, List<GoodsQuestion> goodsQuestions, List<GoodsProcess> goodsProcessList) {
//		if (goodsOpeningProcessService.checkGoodsOpeningProcess(goodsOpeningProcesses)) {
//			throw new ServiceException("请检查产品开通流程是否填写完整!");
//		}
//		if (goodsQuestionService.checkGoodsQuestions(goodsQuestions)) {
//			throw new ServiceException("请检查产品问题是否填写完成!");
//		}
//		if (goodsExpenseRelationService.checkGoodsExpenseRelations(goodsExpenseRelations)) {
//			throw new ServiceException("请检查产品费用是否填写完成!");
//		}
//		if (goodsMaterialService.checkGoodsMaterials(goodsMaterials)) {
//			throw new ServiceException("请检查产品资料是否填写完整!");
//		}
//		if (goodsContractTemplateService.checkGoodsContractTemplates(goodsContractTemplates)) {
//			throw new ServiceException("请检查合同模板信息是否填写完整!");
//		}
//		if (goodsProcessService.checkGoodsProcess(goodsProcessList)) {
//			throw new ServiceException("请检查产品流程是否填写!");
//		}
//		checkGoodsInfo(goods);
////		if (checkGoodsInfoIsComplete(goods)){
////			throw new ServiceException("请检查基本数据是否填写完整!");
////		}
//		//检查重复合同模板
//		contractService.checkDuplicateContracts(goodsContractTemplates);
//		//检查重复的资料收集节点
//		goodsMaterialService.checkDuplicateMaterials(goodsMaterials);
//		return false;
//	}
//
//	private void checkGoodsInfo(Goods goods) {
//		String background = goods.getBackground();
//		Long creditFormId = goods.getCreditFormId();
//		Long coreCreditFormId = goods.getCoreCreditFormId();
//		//Long whiteListTemplateId = goods.getWhiteListTemplateId();
//		Long scoreTemplateId = goods.getScoreTemplateId();
//
//		Long capitalId = goods.getCapitalId();
//		//收费方式 1 资方统一收费 2 平台资方单独收取
//		Integer chargeMethod = goods.getChargeMethod();
//		if (StringUtil.isBlank(background)) {
//			throw new ServiceException("产品背景不能为空!");
//		}
//		if (Objects.isNull(creditFormId)) {
//			throw new ServiceException("融资企业表单不能为空!");
//		}
//		if (Objects.isNull(coreCreditFormId)) {
//			throw new ServiceException("核心企业表单不能为空!");
//		}
//		/*if (Objects.isNull(whiteListTemplateId)) {
//			throw new ServiceException("企业白名单不能为空!");
//		}*/
//		if (Objects.isNull(scoreTemplateId)) {
//			throw new ServiceException("评分模板不能为空!");
//		}
//		if (capitalId != null) {
//			checkCapitalAccountIsComplete(capitalId, goods.getId());
//		}
//
//		if (CapitalTypeEnum.PLATFORM_CHARGE_MODE.getCode().equals(chargeMethod)) {
//			checkPlatformCostPayModeIsComplete(goods.getId());
//		}
//
//	}
//
//	private void checkPlatformCostPayModeIsComplete(Long goodsId) {
////		BillBankCardaRelation cardaRelation = billBankCardaRelationService.getBillBankCardaByGoodsId(goodsId, AccountTypeEnum.PLATFORM_ACCOUNT.getCode());
////		Assert.notNull(cardaRelation, "平台费用支付方式不能为空");
////		if (CapitalTypeEnum.CAPITAL_TYPE_BANK.getCode().equals(cardaRelation.getPlatformCostPayMode())) {
////			Assert.notNull(cardaRelation.getBillBankCardaId(), "平台账户不能为空");
////		}
//	}
//
//	private void checkCapitalAccountIsComplete(Long capitalId, Long goodsId) {
//		Dept capital = deptService.getById(capitalId);
//		if (!CapitalTypeEnum.CAPITAL_TYPE_BANK.getCode().equals(capital.getCapitalType())) {
//			BillBankCardaRelation cardaRelation = billBankCardaRelationService.getBillBankCardaByGoodsId(goodsId, AccountTypeEnum.CAPITAL_ACCOUNT.getCode());
//			Assert.notNull(cardaRelation, "资方账户不能为空");
//		}
//	}
//
//	private boolean checkGoodsIsComplete(Goods goods,
//										 List<GoodsMaterial> goodsMaterials,
//										 List<GoodsExpenseRelation> goodsExpenseRelations,
//										 List<GoodsContractTemplateVO> goodsContractTemplates,
//										 List<GoodsOpeningProcess> goodsOpeningProcess,
//										 List<GoodsQuestion> goodsQuestions,
//										 List<GoodsProcess> goodsProcessList) {
//		return checkGoodsInfoIsComplete(goods)
//			|| goodsContractTemplateService.checkGoodsContractTemplates(goodsContractTemplates)
//			|| goodsMaterialService.checkGoodsMaterials(goodsMaterials)
//			|| goodsOpeningProcessService.checkGoodsOpeningProcess(goodsOpeningProcess)
//			|| goodsQuestionService.checkGoodsQuestions(goodsQuestions)
//			|| goodsExpenseRelationService.checkGoodsExpenseRelations(goodsExpenseRelations)
//			|| goodsProcessService.checkGoodsProcess(goodsProcessList);
//	}
//
//	private boolean checkGoodsInfoIsComplete(Goods goods) {
//		Map<String, Object> map = JSON.parseObject(JSON.toJSONString(goods, SerializerFeature.WriteMapNullValue), Map.class);
//		List<String> ignoreFields = Lists.newArrayList("bondPayProportion",
//			"bondPayType",
//			"bondReleaseMode",
//			"isPayBond",
//			"discount",
//			"commodityWhiteListId",
//			"bondPayProportionStart",
//			"interestDay",
//			"isDelay",
//			"delayType",
//			"delayInterestRateMin",
//			"delayInterestRateMax",
//			"delayBeforeDayMin",
//			"delayBeforeDayMax"
//		);
//		for (Map.Entry<String, Object> entry : map.entrySet()) {
//			String key = entry.getKey();
//			if (ignoreFields.contains(key)) {
//				continue;
//			}
//			Object value = entry.getValue();
//			boolean checkResult;
//			if (value instanceof String) {
//				checkResult = StringUtil.isBlank((CharSequence) value);
//			} else {
//				checkResult = Objects.isNull(value);
//			}
//			if (checkResult) {
//				return true;
//			}
//		}
//		Integer type = goods.getType();
//		BigDecimal loanableStart = goods.getLoanableStart();
//		BigDecimal loanableEnd = goods.getLoanableEnd();
//
//		boolean result = type.equals(GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE.getCode())
//			&& Objects.isNull(loanableStart) && Objects.isNull(loanableEnd);
//
//		BigDecimal bondPayProportion = goods.getBondPayProportion();
//		Integer bondPayType = goods.getBondPayType();
//		Integer bondReleaseMode = goods.getBondReleaseMode();
//		Integer isPayBond = goods.getIsPayBond();
//		Long commodityWhiteListId = goods.getCommodityWhiteListId();
//
//		boolean result1 = type.equals(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode())
//			&& Func.hasEmpty(bondPayProportion, bondPayType, bondReleaseMode, isPayBond, commodityWhiteListId);
//		return result || result1;
//	}
//
//	public void checkGoodsIsOnShelf(Long goodsId) {
//		Goods dbGoods = baseMapper.selectById(goodsId);
//		if (canOperator() && !GoodsEnum.UN_ON_SHELF.getCode().equals(dbGoods.getStatus())) {
//			throw new ServiceException("修改失败，仅未上架商品才可修改");
//		}
//	}
//
//	private void checkGoodsParamIsCorrect(Goods goods) {
//		Assert.isFalse(goods.getRepaymentType().equals(GoodsEnum.AMORTIZATION_LOAN.getCode())
//			&& goods.getLoadTermUnit().equals(GoodsEnum.DAY.getCode()), "如果选择分期付款，则借款期限只能以【期】为单位，不能以【天】为单位。");
//		Assert.isFalse(goods.getType().equals(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode())
//			&& !goods.getBankCardCollectionType().equals(GoodsEnum.ENTRUSTED_PAYMENT.getCode()), "代采融资只能选择受托支付。");
//		Assert.isFalse(goods.getLoanAmountStart().compareTo(goods.getLoanAmountEnd()) >= 0, "借款金额错误");
//		Assert.isFalse(goods.getLoadTermStart().compareTo(goods.getLoadTermEnd()) >= 0, "借款期限错误");
//	}
//
//
//	/**
//	 * 根据产品id以及定时任务类型，获取倒计时时间
//	 *
//	 * @param goodsId 产品id
//	 * @param type    类型
//	 * @return 倒计时时间：LocalDateTime
//	 */
//	@Override
//	public LocalDateTime getByExpireTime(Long goodsId, Integer type) {
//		return goodsTimingService.getByExpireTime(goodsId, type);
//	}
//
//
//	@Override
//	public List<GoodsVO> getGoodsList() {
//		//状态2 已上架
//		List<Goods> goods = baseMapper.selectList(Wrappers.<Goods>lambdaQuery().eq(BaseEntity::getStatus, 2));
//		if (CollUtil.isEmpty(goods)) {
//			return Collections.emptyList();
//		}
//		List<GoodsVO> goodsVOS = goods.stream().map(good -> {
//			GoodsVO goodsVO = new GoodsVO();
//			goodsVO.setId(good.getId());
//			goodsVO.setGoodsName(good.getGoodsName());
//			return goodsVO;
//		}).collect(Collectors.toList());
//		return goodsVOS;
//	}
//
//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public GoodsDTO copyById(Long id) {
//		GoodsVO detail = detail(id);
//		List<Long> labelIds = detail.getLabelIds();
//		GoodsDTO goods = new GoodsDTO();
//		BeanUtil.copyProperties(detail, goods, "goodsCode", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//		goods.setStatus(CommonConstant.OFF_RELEASE);
//		goods.setGoodsName(goods.getGoodsName().concat("copy").concat(RandomUtil.randomNumbers(3)));
//		goods.setGoodsLabelIds(labelIds);
//		goods.setIsHighQuality(2);
//		List<GoodsQuestion> goodsQuestions = goods.getGoodsQuestions();
//		if (CollUtil.isNotEmpty(goodsQuestions)) {
//			goodsQuestions = goodsQuestions.stream().map(e -> {
//				GoodsQuestion newTemp = new GoodsQuestion();
//				BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//				return newTemp;
//			}).collect(Collectors.toList());
//			goods.setGoodsQuestions(goodsQuestions);
//		}
//		List<GoodsOpeningProcess> goodsOpeningProcesses = goods.getGoodsOpeningProcesses();
//		if (CollUtil.isNotEmpty(goodsOpeningProcesses)) {
//			goodsOpeningProcesses = goodsOpeningProcesses.stream().map(e -> {
//				GoodsOpeningProcess newTemp = new GoodsOpeningProcess();
//				BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//				return newTemp;
//			}).collect(Collectors.toList());
//			goods.setGoodsOpeningProcesses(goodsOpeningProcesses);
//		}
//		List<GoodsMaterialVO> goodsMaterialVOS = goodsMaterialService.selectGoodsMaterial(id);
//		if (CollUtil.isNotEmpty(goodsMaterialVOS)) {
//			List<GoodsMaterialDTO> copy = BeanUtil.copy(goodsMaterialVOS, GoodsMaterialDTO.class);
//			if (CollUtil.isNotEmpty(copy)) {
//				copy = copy.stream().map(e -> {
//					GoodsMaterialDTO newTemp = new GoodsMaterialDTO();
//					BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//					return newTemp;
//				}).collect(Collectors.toList());
//			}
//			goods.setGoodsMaterials(copy);
//		}
//
//		List<GoodsTiming> list = goodsTimingService.lambdaQuery()
//			.eq(GoodsTiming::getGoodsId, id)
//			.list();
//		if (CollUtil.isNotEmpty(list)) {
//			list = list.stream().map(e -> {
//				GoodsTiming newTemp = new GoodsTiming();
//				BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//				return newTemp;
//			}).collect(Collectors.toList());
//			goods.setGoodsTimingList(list);
//		}
//
//		List<GoodsContractTemplateVO> goodsContractTemplateListByGoodsId = goodsContractTemplateService.getGoodsContractTemplateListByGoodsId(id);
//		if (CollUtil.isNotEmpty(goodsContractTemplateListByGoodsId)) {
//			List<GoodsContractTemplateDTO> copy = BeanUtil.copy(goodsContractTemplateListByGoodsId, GoodsContractTemplateDTO.class);
//			if (CollUtil.isNotEmpty(copy)) {
//				copy = copy.stream().map(e -> {
//					GoodsContractTemplateDTO newTemp = new GoodsContractTemplateDTO();
//					BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//					return newTemp;
//				}).collect(Collectors.toList());
//			}
//			goods.setGoodsContractTemplates(copy);
//		}
//
//		Map<Integer, List<GoodsExpenseRelationVO>> integerListMap = StreamUtil.groupBy(goodsExpenseRelationService.getByGoodsId(id), GoodsExpenseRelationVO::getType);
//		if (CollUtil.isNotEmpty(integerListMap)) {
//			List<GoodsExpenseRelation> capitalExpenses = BeanUtil.copy(Objects.requireNonNull(integerListMap.get(1)), GoodsExpenseRelation.class);
//			if (CollUtil.isNotEmpty(capitalExpenses)) {
//				capitalExpenses = capitalExpenses.stream().map(e -> {
//					GoodsExpenseRelation newTemp = new GoodsExpenseRelation();
//					BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//					return newTemp;
//				}).collect(Collectors.toList());
//			}
//			List<GoodsExpenseRelation> platFromExpense = BeanUtil.copy(Objects.requireNonNull(integerListMap.get(2)), GoodsExpenseRelation.class);
//			if (CollUtil.isNotEmpty(platFromExpense)) {
//				platFromExpense = platFromExpense.stream().map(e -> {
//					GoodsExpenseRelation newTemp = new GoodsExpenseRelation();
//					BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//					return newTemp;
//				}).collect(Collectors.toList());
//			}
//			goods.setPlatformExpenses(platFromExpense);
//			goods.setCapitalExpenses(capitalExpenses);
//		}
//
//		Map<Integer, List<BillBankCardaRelationVO>> collect = billBankCardaRelationService.getByGoodsId(id).stream()
//			.collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getAccountType()).orElse(1)));
//		if (CollUtil.isNotEmpty(collect)) {
//			List<BillBankCardaRelationVO> platAccount = collect.get(1);
//			if (CollUtil.isNotEmpty(platAccount)) {
//				platAccount = platAccount.stream().map(e -> {
//					BillBankCardaRelationVO newTemp = new BillBankCardaRelationVO();
//					BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//					return newTemp;
//				}).collect(Collectors.toList());
//				goods.setPlatformBillBankCardas(platAccount.get(0));
//			}
//			List<BillBankCardaRelationVO> capitalAccount = collect.get(2);
//			if (CollUtil.isNotEmpty(capitalAccount)) {
//				capitalAccount = capitalAccount.stream().map(e -> {
//					BillBankCardaRelationVO newTemp = new BillBankCardaRelationVO();
//					BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//					return newTemp;
//				}).collect(Collectors.toList());
//				goods.setCapitalBillBankCardas(capitalAccount.get(0));
//			}
//		}
//		List<GoodsProcess> goodsProcesses = goodsProcessService.listByGoodsId(id);
//		goodsProcesses = goodsProcesses.stream().map(e -> {
//			GoodsProcess newTemp = new GoodsProcess();
//			BeanUtil.copyProperties(e, newTemp, "goodsId", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//			return newTemp;
//		}).collect(Collectors.toList());
//		goods.setGoodsProcessList(goodsProcesses);
//		saveGoods(goods);
//		return goods;
//	}
//
    @Override
    public Boolean canOperator() {
        String key = "goods_operator_ability";
        String value = paramService.getValue(key);
        return StringUtil.isNotBlank(value) ? Boolean.valueOf(value) : false;
    }
//	@Override
//	public String getGoodsSceneId(Long goodsId) {
//		Goods goods = goodsMapper.selectOne(Wrappers.<Goods>lambdaQuery()
//			.eq(BaseEntity::getId, goodsId));
//		if (ObjectUtil.isEmpty(goods)) {
//			return "";
//		}
//		Long ruleSceneId = goods.getRuleSceneId();
//		if (ObjectUtil.isEmpty(ruleSceneId)) {
//			return "";
//		}
//		DroolsSceneInfo droolsSceneInfo = sceneInfoMapper.selectOne(Wrappers.<DroolsSceneInfo>lambdaQuery().eq(BaseEntity::getId, ruleSceneId));
//		return droolsSceneInfo.getSceneIdentify();
//	}

    @Override
    public boolean checkQuotaApplyParam(Map<String, Object> variables) {
        FinalApproveAmount finalApproveAmount = JSON.parseObject(JSON.toJSONString(variables.get(ProcessConstant.FINAL_APPROVE_AMOUNT)), FinalApproveAmount.class);
        BigDecimal financingProportion = finalApproveAmount.getFinancingProportion();
//        if (DepositYNEnum.DEPOSIT_PAY_YES.getCode().equals(isPayBond) && financingProportion.compareTo(BigDecimal.valueOf(100)) != 0) {

        //赎货类
        Boolean needRedeem = (Boolean) variables.get("needRedeem");
        if (ObjectUtil.isNotEmpty(needRedeem) && needRedeem) {
            //产品类型为赎货类时，融资占比必须100%
            if (financingProportion.compareTo(BigDecimal.valueOf(100)) != 0) {
                throw new ServiceException("产品类型为赎货类时，融资占比必须100%");
            }
            //需要缴纳保证金时，保证金必须在配置范围之间
            Integer isPayBond = (Integer) variables.get(ProcessConstantExtend.IS_PAY_BOND);
            if (ObjectUtil.isNotEmpty(isPayBond) && DepositYNEnum.DEPOSIT_PAY_YES.getCode().equals(isPayBond)) {
                BigDecimal bondProportion = finalApproveAmount.getBondProportion();
                OrderFinancingGoodsVo goodsVo = JSONUtil.toBean(JSONUtil.parseObj(variables.get(ProcessConstant.PROCESS_GOODS_INFO)), OrderFinancingGoodsVo.class);
                BigDecimal bondStart = goodsVo.getBondPayProportionStart();
                BigDecimal bondEnd = goodsVo.getBondPayProportionEnd();
                if (ObjectUtil.isNotEmpty(bondProportion) && null != bondStart && null != bondEnd) {
                    if (bondProportion.compareTo(bondStart) < 0 || bondProportion.compareTo(bondEnd) > 0) {
                        throw new ServiceException(String.format("保证金比例必须在%s%%至%s%%之间", bondStart, bondEnd));
                    }
                }
            }
        }

        if (ObjectUtil.isNotEmpty(financingProportion) && (financingProportion.compareTo(BigDecimal.valueOf(100)) > 0 || financingProportion.compareTo(BigDecimal.ZERO) < 0)) {
            throw new ServiceException("融资占比必须在0-100%之间");
        }

        return true;
    }

}
