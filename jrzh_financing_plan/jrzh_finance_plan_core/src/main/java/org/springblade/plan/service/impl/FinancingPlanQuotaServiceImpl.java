/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springblade.customer.entity.CustomerGoods;
import org.springblade.customer.entity.EnterpriseQuota;
import org.springblade.customer.service.ICustomerGoodsService;
import org.springblade.customer.service.IEnterpriseQuotaService;
import org.springblade.plan.entity.FinancingPlanQuota;
import org.springblade.plan.vo.FinancingPlanQuotaVO;
import org.springblade.plan.mapper.FinancingPlanQuotaMapper;
import org.springblade.plan.service.IFinancingPlanQuotaService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;


import java.util.List;

/**
 * 融资方案配额表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
@RequiredArgsConstructor
public class FinancingPlanQuotaServiceImpl extends BaseServiceImpl<FinancingPlanQuotaMapper, FinancingPlanQuota> implements IFinancingPlanQuotaService {
	private final ICustomerGoodsService customerGoodsService;
	private final ProductDirector productDirector;
	private final IEnterpriseQuotaService enterpriseQuotaService;

	@Override
	public IPage<FinancingPlanQuotaVO> selectFinancingPlanQuotaPage(IPage<FinancingPlanQuotaVO> page, FinancingPlanQuota financingPlanQuota) {
		List<FinancingPlanQuota> financingPlanQuotas = baseMapper.selectFinancingPlanQuotaPage(page, financingPlanQuota);
		List<FinancingPlanQuotaVO> financingPlanQuotaVOs = BeanUtil.copyToList(financingPlanQuotas, FinancingPlanQuotaVO.class);
		return page.setRecords(financingPlanQuotaVOs);
	}

	@Override
	public List<FinancingPlanQuotaVO> listWithProduct(String planNo) {
		List<FinancingPlanQuota> financingPlanQuotas = this.list(Wrappers.<FinancingPlanQuota>lambdaQuery().eq(FinancingPlanQuota::getPlanNo, planNo));
		List<FinancingPlanQuotaVO> financingPlanQuotaVos = BeanUtil.copyToList(financingPlanQuotas, FinancingPlanQuotaVO.class);
		financingPlanQuotaVos.forEach( e-> {
			CustomerGoods customerGoods = customerGoodsService.getById(e.getCustomerGoodsId());
			e.setProduct(productDirector.detail(customerGoods.getGoodsId()));
			EnterpriseQuota enterpriseQuota = enterpriseQuotaService.getById(customerGoods.getEnterpriseQuotaId());
			e.setEnterpriseQuota(enterpriseQuota);
		});

		return financingPlanQuotaVos;
	}

	@Override
	public List<FinancingPlanQuota> getByPlanNo(String planNo) {
		return this.list(Wrappers.<FinancingPlanQuota>lambdaQuery().eq(FinancingPlanQuota::getPlanNo, planNo));
	}


}
