/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.controller.front;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.Condition;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.finance.dto.financeApplyHandler.FinanceApplyHandlerDTO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.service.impl.FinanceApplyServiceImpl;
import org.springblade.plan.dto.FinancingPlanBasicDTO;
import org.springblade.plan.dto.TradingOrderDataQueryDTO;
import org.springblade.plan.entity.FinancingPlanBasic;
import org.springblade.plan.entity.FinancingPlanQuota;
import org.springblade.plan.entity.FinancingPlanQuotaBind;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.enums.FinancingPlanBasicStatusEnum;
import org.springblade.plan.enums.PlanCalculateSortTypeEnum;
import org.springblade.plan.service.IFinancingPlanBasicService;
import org.springblade.plan.service.IFinancingPlanQuotaBindService;
import org.springblade.plan.service.IFinancingPlanQuotaService;
import org.springblade.plan.service.ITradingOrderDataService;
import org.springblade.plan.vo.FinancingPlanBasicVO;
import org.springblade.plan.wrapper.FinancingPlanBasicWrapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 融资方案基础信息 控制器
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-plan/" + CommonConstant.WEB_FRONT + "/financing-plan-basic")
@Api(value = "融资方案基础信息前端", tags = "融资方案基础信息接口")
public class FrontFinancingPlanBasicController extends BladeController {

    private final IFinancingPlanBasicService financingPlanBasicService;
    private final IFinancingPlanQuotaService financingPlanQuotaService;
    private final IFinancingPlanQuotaBindService financingPlanQuotaBindService;
    private final FinanceApplyServiceImpl financeApplyServiceImpl;
    private final ITradingOrderDataService tradingOrderDataService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入financingPlanBasic")
    public R<FinancingPlanBasicVO> detail(FinancingPlanBasic financingPlanBasic) {
        FinancingPlanBasic detail = financingPlanBasicService.getOne(Condition.getQueryWrapper(financingPlanBasic));
        return R.data(FinancingPlanBasicWrapper.build().entityVO(detail));
    }

    /**
     * 获取融资进度和融资申请ids
     */
    @GetMapping("/getNodeAndApply")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "获取融资进度和融资申请ids", notes = "传入planId")
    public R<FinancingPlanBasicVO> getNodeAndApply(@RequestParam Long planId) {
        FinancingPlanBasic one = financingPlanBasicService.getById(planId);
        if (ObjectUtil.isEmpty(one)) {
            return R.data(null);
        }
        List<FinancingPlanQuota> financingPlanQuotas = financingPlanQuotaService.list(Wrappers.<FinancingPlanQuota>lambdaQuery().eq(FinancingPlanQuota::getPlanNo, one.getPlanNo()));
        List<Long> financeIds = financingPlanQuotas.stream().map(FinancingPlanQuota::getFinanceId).collect(Collectors.toList());
        FinancingPlanBasicVO detail = BeanUtil.copyProperties(one, FinancingPlanBasicVO.class);
        //获取订单数据
        List<Long> quotaIds = CollStreamUtil.toList(financingPlanQuotas, FinancingPlanQuota::getId);
        List<FinancingPlanQuotaBind> binds = financingPlanQuotaBindService.list(Wrappers.<FinancingPlanQuotaBind>lambdaQuery().in(FinancingPlanQuotaBind::getFinancingQuotaId, quotaIds));
        List<Long> orderIds = CollStreamUtil.toList(binds, FinancingPlanQuotaBind::getOrderId);
        List<TradingOrderData> orderList = tradingOrderDataService.listByIds(orderIds);
        if (detail != null) {
            detail.setFinanceApplyIds(financeIds);
            detail.setOrderList(orderList);
        }
        return R.data(detail);
    }

    /**
     * 获取当前方案调整后的新方案
     */
    @GetMapping("/getNewPlan")
    public R<Long> getNewPlan(@RequestParam Long planId) {
        FinancingPlanBasic newPlan = financingPlanBasicService.getOne(Wrappers.<FinancingPlanBasic>lambdaQuery()
                .eq(FinancingPlanBasic::getUserId, AuthUtil.getUserId())
                .eq(FinancingPlanBasic::getOldPlanId, planId)
                .eq(FinancingPlanBasic::getStatus, FinancingPlanBasicStatusEnum.PLAN_IN_USE.getCode()));
        if (ObjectUtil.isEmpty(newPlan)) {
            return R.fail("当前方案的调整方案不存在");
        }
        return R.data(newPlan.getId());
    }

    /**
     * 获取当前方案的融资申请提交数据
     */
    @GetMapping("/getPlanFinancingApplyInfo")
    public R<FinancingPlanBasicDTO> getPlanFinancingApplyInfo(@RequestParam Long planId) {
        FinancingPlanBasic plan = financingPlanBasicService.getById(planId);

        if (ObjectUtil.isEmpty(plan)) {
            return R.fail("当前方案的调整方案不存在");
        }
        FinancingPlanBasicDTO financingPlanBasicDTO = BeanUtil.copyProperties(plan, FinancingPlanBasicDTO.class);
        List<FinanceApplyHandlerDTO> list = JSON.parseObject(plan.getFinancingApplyInfo(), List.class);
        if (null == financingPlanBasicDTO) {
            return R.fail("当前方案的融资申请提交数据不存在");
        }
        financingPlanBasicDTO.setFinanceApplyHandlerDTOList(list);

        return R.data(financingPlanBasicDTO);
    }


    /**
     * 更新所有方案
     */
    @GetMapping("/allPlanCalculate")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "更新所有方案", notes = "传入groupId")
    public R<Boolean> allPlanCalculate(@RequestParam Long groupId) {
        Long userId = AuthUtil.getUserId();
        return R.data(financingPlanBasicService.allGroupPlanRecalculate(userId));
    }

    /**
     *
     */
    @GetMapping("/planCalculateByAmount")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "根据客户输入的金额计算方案")
    public R<FinancingPlanBasicDTO> planCalculateByAmount(@RequestParam BigDecimal customerEnterAmount, Long goodsId, Integer sortType, Long planId, String planNo) {
        Long userId = AuthUtil.getUserId();
        if (null == sortType) {
            sortType = PlanCalculateSortTypeEnum.SORT_BY_MAXIMUM_LIMIT.getCode();
        }
        return R.data(financingPlanBasicService.planCalculateByAmount(userId, customerEnterAmount, goodsId, sortType, planId, planNo));
    }

    /**
     * 分页 融资方案基础
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "方案分页", notes = "传入financingPlanBasic")
    public R<IPage<FinancingPlanBasicVO>> list(FinancingPlanBasicDTO financingPlanBasic, Query query) {
        financingPlanBasic.setUserId(AuthUtil.getUserId());
        IPage<FinancingPlanBasic> pages = financingPlanBasicService.page(Condition.getPage(query), Condition.getQueryWrapper(financingPlanBasic, FinancingPlanBasic.class).lambda()
                .orderByDesc(FinancingPlanBasic::getUpdateTime));
        return R.data(FinancingPlanBasicWrapper.build().pageVO(pages));
    }

    /**
     * 融资列表分页
     */
    @GetMapping("/financePlanList")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入financingPlanBasic")
    public R<IPage<FinancingPlanBasicVO>> financePlanList(FinancingPlanBasicDTO financingPlanBasic, Query query) {
        financingPlanBasic.setUserId(AuthUtil.getUserId());
        IPage<FinancingPlanBasic> pages = financingPlanBasicService.page(Condition.getPage(query), Condition.getQueryWrapper(financingPlanBasic, FinancingPlanBasic.class).lambda()
                .orderByDesc(FinancingPlanBasic::getUpdateTime));
        Page<FinancingPlanBasicVO> pageVO = FinancingPlanBasicWrapper.build().pageVO(pages);
        pageVO.getRecords().forEach(item -> {
            List<FinancingPlanQuota> financingPlanQuotas = financingPlanQuotaService.list(Wrappers.<FinancingPlanQuota>lambdaQuery().eq(FinancingPlanQuota::getPlanNo, item.getPlanNo()));
            List<Long> financeIds = CollStreamUtil.toList(financingPlanQuotas, FinancingPlanQuota::getFinanceId);
            if (CollUtil.isNotEmpty(financeIds)) {
                List<FinanceApply> financeApplies = financeApplyServiceImpl.listByIds(financeIds);
                Map<String, Integer> map = CollStreamUtil.toMap(financeApplies, FinanceApply::getGoodsName, FinanceApply::getStatus);
                item.setFinanceApplyAndStatus(map);
            }
        });
        return R.data(pageVO);
    }

    /**
     * 自定义分页 融资方案基础表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入financingPlanBasic")
    @PreAuth("hasPermission('financingPlan:financingPlanBasic:page') or hasRole('administrator')")
    public R<IPage<FinancingPlanBasicVO>> page(FinancingPlanBasicVO financingPlanBasic, Query query) {
        IPage<FinancingPlanBasicVO> pages = financingPlanBasicService.selectFinancingPlanBasicPage(Condition.getPage(query), financingPlanBasic);
        return R.data(pages);
    }

    /**
     * 根据订单ID获取融资方案列表
     */
    @GetMapping("/getByOrder")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "根据订单ID获取融资方案列表", notes = "传入orderId,groupId")
    public R<List<FinancingPlanBasicVO>> getFinancingPlansByOrder(@RequestParam List<Long> orderIds, Long groupId) {
        Long userId = AuthUtil.getUserId();
        if (1 == orderIds.size()) {
            Long orderId = orderIds.get(0);
            List<FinancingPlanBasicVO> plans = financingPlanBasicService.getFinancingPlansByOrder(userId, orderId, groupId);
            if (CollectionUtils.isEmpty(plans)) {
                return R.fail("该订单无法融资当前融资产品，请重新选择");
            }
            return R.data(plans);

        } else {
            //多对一计算
            FinancingPlanBasicVO plan = financingPlanBasicService.getFinancingPlansByOrderList(userId, orderIds, groupId);
            return R.data(Collections.singletonList(plan));
        }
    }

    /**
     * 查询可融资使用的订单列表
     */
    @GetMapping("/myFinanceUseOrders")
    @ApiOperation(value = "查询可融资使用的订单列表")
    public R<List<TradingOrderData>> myFinanceUseOrders(TradingOrderDataQueryDTO tradingOrderData) {
        // 设置当前用户ID
        tradingOrderData.setUserId(AuthUtil.getUserId());
        return R.data(financingPlanBasicService.myFinanceUseOrders(tradingOrderData));
    }


}
