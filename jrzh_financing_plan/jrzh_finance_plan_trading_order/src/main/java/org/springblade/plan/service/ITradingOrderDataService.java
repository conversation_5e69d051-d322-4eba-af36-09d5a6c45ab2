/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.plan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.plan.dto.CreateOrderDTO;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.vo.TradingOrderDataVO;

import java.math.BigDecimal;

/**
 * 测试订单数据表 服务类
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
public interface ITradingOrderDataService extends BaseService<TradingOrderData> {

	/**
	 * 生成订单数据
	 */
	TradingOrderData createOrder(Long userId, BigDecimal orderAmount,Integer scenarioType);

	/**
	 * 生成订单数据
	 */
	TradingOrderData createOrder(CreateOrderDTO orderDTO);

	/**
	 * 自定义分页查询
	 *
	 * @param page 分页对象
	 * @param tradingOrderData 查询条件
	 * @return 分页结果
	 */
	IPage<TradingOrderDataVO> selectTradingOrderDataPage(IPage<TradingOrderData> page, TradingOrderDataVO tradingOrderData);

}
