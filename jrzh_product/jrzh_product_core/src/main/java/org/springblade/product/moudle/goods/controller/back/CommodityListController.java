/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.product.moudle.goods.controller.back;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.CodeEnum;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.product.common.constant.CommodityCatalogueEnum;
import org.springblade.product.common.constant.CommodityEnum;
import org.springblade.product.common.constant.CommoditySpecEnum;
import org.springblade.product.common.constant.CommodityUnitEnum;
import org.springblade.product.common.dto.AttachDTO;
import org.springblade.product.common.dto.CommodityListDTO;
import org.springblade.product.common.dto.OffOrPutShelfDTO;
import org.springblade.product.common.entity.*;
import org.springblade.product.common.vo.CommodityListVO;
import org.springblade.product.common.vo.CommoditySpecVO;
import org.springblade.product.moudle.goods.service.*;
import org.springblade.product.moudle.goods.wrapper.CommodityListWrapper;
import org.springblade.product.moudle.goods.wrapper.CommoditySpecWrapper;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.service.IAttachService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2021-12-17
 */
@RestController
@AllArgsConstructor
@RequestMapping(CommonConstant.BLADE_COMMODITY + CommonConstant.WEB_BACK + "/commoditylist")
@Api(value = "商品列表", tags = "接口")
public class CommodityListController extends BladeController {

    private final ICommodityListService commodityListService;
    private final IAttachService attachService;
    private final ICommoditySpecService commoditySpecService;
    private final ICommodityCatalogueService commodityCatalogueService;
   // private final ICustomerSupplierClient customerSupplierService;
    private final ICommodityUnitService commodityUnitService;
    private final ICommodityWhiteListCommodityService commodityWhiteListCommodityService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入commodityList")
    public R<CommodityListVO> detail(CommodityList commodityList) {
        // 如果参数有1项为空，提示，参数校验失败
        if (!ObjectUtil.isAllNotEmpty(commodityList, commodityList.getId())) {
            return R.fail("参数id为空，校验失败");
        }

        // 根据id查询商品，如果为空，提示，该id对应的商品数据不存在
        CommodityList commodityList1 = commodityListService.getById(commodityList.getId());
        if (ObjectUtil.isEmpty(commodityList1)) {
            return R.fail("该id对应的商品数据不存在");
        }

        // 将商品转成商品VO
        CommodityListVO commodityListVO = CommodityListWrapper.build().entityVO(commodityList1);

        // 根据附件ids，查询附件表，得到附件List，如果不为空，设置到commodityListVO中
        List<Attach> attachList = attachService.listByIds(Func.toLongList(commodityListVO.getAttachCommodityId()));
        if (CollUtil.isNotEmpty(attachList)) {
            // 将List<Attach>拷贝并转化为List<AttachDTO>
            List<AttachDTO> attachDTOS = BeanUtil.copyToList(attachList, AttachDTO.class);
            // 设置attachId的值
            attachDTOS.forEach(e -> e.setAttachId(e.getId().toString()));

            // 将附件列表设置到commodityListVO
            commodityListVO.setAttachList(attachDTOS);
        }

        // 获取规格列表
        List<CommoditySpec> commoditySpecList = commoditySpecService.list(Wrappers.<CommoditySpec>lambdaQuery().eq(CommoditySpec::getCommodityListId, commodityList.getId()));

        // 将商品List规格转成商品VOList
        List<CommoditySpecVO> commoditySpecVOS = CommoditySpecWrapper.build().listVO(commoditySpecList);

        commodityListVO.setCommoditySpecList(commoditySpecVOS);
        return R.data(commodityListVO);
    }

    /**
     * 分页
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入commodityList")
    public R<IPage<CommodityListVO>> list(CommodityList commodityList, Query query) {
        IPage<CommodityList> pages = commodityListService.page(Condition.getPage(query), Condition.getQueryWrapper(commodityList));
        return R.data(CommodityListWrapper.build().pageVO(pages));
    }

    /**
     * 自定义分页
     * Request URL: http://localhost:1888/api/blade-commodity/web-back/commoditylist/page?no=J&name=%E5%95%86%E5%93%81&commodityCatalogueName=1475655444398829570&supplierId=1500677584982114306&status=0&current=1&size=10
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "商品自定义分页", notes = "传入commodityList")
    public R<IPage<CommodityListVO>> page(CommodityListVO commodityList, Query query) {
        IPage<CommodityListVO> pages = commodityListService.selectCommodityListPage(Condition.getPage(query), commodityList);
        return R.data(pages);
    }

    /**
     * 根据商品ids查询商品列表
     */
    @GetMapping("/selectCommodityListByIds")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "商品列表", notes = "ids")
    public R<List<CommodityListVO>> selectCommodityListByIds(String ids) {
        // 如果ids为空，返回null
        if (StrUtil.isBlank(ids)) {
            return R.data(Collections.EMPTY_LIST);
        }
        List<Long> longIds = Convert.toList(Long.class, ids);

        // 根据商品longIds，查询商品表，得到商品列表信息
        List<CommodityList> commodityLists = commodityListService.listByIds(longIds);
        List<CommodityListVO> listVO = CommodityListWrapper.build().listVO(commodityLists);

        // 设置分类名称
        setCatalogueName(listVO);

        // 设置附件图标地址
        setAttachPicUrl(listVO);

        // 设置供应商名称
        //setSupplierName(listVO);
        return R.data(listVO);
    }

//    /**
//     * 设置供应商名称
//     *
//     * @param listVO
//     */
//    private void setSupplierName(List<CommodityListVO> listVO) {
//        List<Long> map = StreamUtil.map(listVO, CommodityListVO::getSupplierId);
//        Map<Long, String> customerSupplierMap = StreamUtil.toMap(customerSupplierService.listByIds(map).getData(), CustomerSupplier::getId, CustomerSupplier::getSupperName);
//        for (CommodityListVO vo : listVO) {
//            Long commoditySupplierId = vo.getSupplierId();
//            if (customerSupplierMap.containsKey(commoditySupplierId)) {
//                vo.setSupplierName(customerSupplierMap.get(commoditySupplierId));
//            }
//        }
//    }

    /**
     * 设置图片路径
     *
     * @param listVO
     */
    private void setAttachPicUrl(List<CommodityListVO> listVO) {
        List<String> map = StreamUtil.map(listVO, CommodityListVO::getAttachCommodityId);
        Map<Long, String> attachMap = StreamUtil.toMap(attachService.listByIds(map), Attach::getId, Attach::getLink);
        for (CommodityListVO vo : listVO) {
            String attachCommodityId = vo.getAttachCommodityId();
            long key = Func.toLong(attachCommodityId);
            if (attachMap.containsKey(key)) {
                vo.setImg(attachMap.get(key));
            }
        }
    }

    /**
     * 设置分类名
     *
     * @param records
     */
    private void setCatalogueName(List<CommodityListVO> records) {
        Map<Long, CommodityCatalogue> stringMap = commodityCatalogueService.list().parallelStream().collect(Collectors.toMap(CommodityCatalogue::getId, e -> e));
        for (CommodityListVO record : records) {
            StringBuilder builder = new StringBuilder();
            Stack<Long> stack = new Stack();
            Long parentId = record.getCatalogueId();

            // 如果id不是顶级父节点，就压栈
            while (!CommonConstant.TOP_PARENT_ID.equals(parentId)) {
                stack.push(parentId);
                // 获取当前id的父id，赋值给当前id
                parentId = stringMap.get(parentId).getParentId();
            }

            // 如果栈不为空，就出栈，并获取对应的分类名称
            while (!stack.isEmpty()) {
                Long popId = stack.pop();
                String directName = stringMap.get(popId).getDirectName();
                builder.append(directName + ">");
            }

            // 删除末尾多余的>
            builder.deleteCharAt(builder.length() - 1);
            record.setCommodityCatalogueName(builder.toString());
        }
    }

    /**
     * 新增
     */
    @SneakyThrows
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入commodityList")
    public R<?> save(@Valid @RequestBody CommodityListDTO commodityListDTO) {
        // 如果有1项参数为空，提示，参数为空，请修改后重试
        if (!ObjectUtil.isAllNotEmpty(commodityListDTO, commodityListDTO.getName(), commodityListDTO.getCatalogueId(),
                commodityListDTO.getSupplierId(), commodityListDTO.getUnitId(), commodityListDTO.getStatus())) {
            return R.fail("参数为空，请修改后重试");
        }

        // 如果附件List为空，由于当前商品只有一张图片，还需判断附件List中附件对象内容是否为空，提示，请先上传图片
        List<AttachDTO> attachList = commodityListDTO.getAttachList();
        if (CollUtil.isEmpty(attachList) || StrUtil.isBlank(attachList.get(0).getAttachId())) {
            return R.fail("请先上传图片");
        }

        // 如果商品规格List为空，提示，商品规格列表不能为空，请重新输入
        List<CommoditySpec> commoditySpecList = commodityListDTO.getCommoditySpecList();
        if (CollUtil.isEmpty(commoditySpecList)) {
            return R.fail("商品规格列表为空，请修改后重试");
        }

        // 提取商品规格List中的规格型号名字，存入到Set<String>中
        Set<String> collectSet = commoditySpecList.stream().map(CommoditySpec::getCommoditySpec).collect(Collectors.toSet());

        // 如果commoditySpecList集合中元素大于等于2，需要判断，商品规格名是否重复
        if (commoditySpecList.size() >= 2 && commoditySpecList.size() > collectSet.size()) {
            return R.fail("商品规格列表中，存在多个相同的规格型号名称");
        }

        // 遍历商品规格List
        for (CommoditySpec commoditySpec : commoditySpecList) {

            // 如果商品规格List中的规格对象，或者对象中数据为空，提示，商品规格列表中有数据为空，请重新输入
            if (!ObjectUtil.isAllNotEmpty(commoditySpec, commoditySpec.getCommoditySpec(), commoditySpec.getCommodityMinPrice(), commoditySpec.getCommodityMaxPrice())) {
                return R.fail("商品规格列表中有数据为空，请修改后重试");
            }

            // 根据商品规格型号名称，查询未删除的商品规格对象（启用/禁用都要查），如果不为空，提示，该商品规格型号已存在，请修改后重试
            CommoditySpec commoditySpec1 = commoditySpecService.getOne(Wrappers.<CommoditySpec>lambdaQuery().eq(CommoditySpec::getCommoditySpec, commoditySpec.getCommoditySpec()));
            if (ObjectUtil.isNotEmpty(commoditySpec1)) {
                return R.fail("该商品规格型号已存在，请修改后重试");
            }

            // 如果最小单价小于0，最大单价小于等于0，或者最小单价高于最大单价，提示，最小单价、最大单价为0，或者最小单价不能高于最大单价
            if (commoditySpec.getCommodityMinPrice().compareTo(BigDecimal.ZERO) < 0 ||
                    commoditySpec.getCommodityMaxPrice().compareTo(BigDecimal.ZERO) <= 0 ||
                    commoditySpec.getCommodityMinPrice().compareTo(commoditySpec.getCommodityMaxPrice()) >= 0) {
                return R.fail("最小单价、最大单价为0，或者最小单价不能高于最大单价");
            }
        }

        // 根据商品名称，查询未被删除的商品对象，如果不为空，提交时，提示，商品名称已经存在，请修改后重试
        CommodityList commodityList = commodityListService.getOne(Wrappers.<CommodityList>lambdaQuery().eq(CommodityList::getName, commodityListDTO.getName()));
        if (ObjectUtil.isNotEmpty(commodityList)) {
            return R.fail("商品名称已经存在，请修改后重试");
        }

        // 根据商品分类id，查询未删除的商品分类对象，如果为空，提示，该商品分类不存在，请修改后重试
        CommodityCatalogue commodityCatalogue = commodityCatalogueService.getOne(Wrappers.<CommodityCatalogue>lambdaQuery().eq(CommodityCatalogue::getId, commodityListDTO.getCatalogueId())
                .eq(CommodityCatalogue::getStatus, CommodityCatalogueEnum.ENABLE.getCode()));
        if (ObjectUtil.isEmpty(commodityCatalogue)) {
            return R.fail("该商品分类不存在，请修改后重试");
        }

        Assert.isTrue(StringUtil.isNotBlank(commodityListDTO.getType()), "商品业务类型不能为空，请修改后重试");
//        // 根据供应商id，查询未删除的供应商对象，如果为空，提示，该供应商不存在，请修改后重试(代采需要填写供应链信息)
//        if (commodityListDTO.getType().indexOf(String.valueOf(GoodsEnum.AGENT_PURCHASE_FINANCING.getCode())) != -1) {
//            CustomerSupplier customerSupplier = customerSupplierService.getOne(commodityListDTO.getSupplierId(), CommonConstant.OPENSTATUS).getData();
//            if (ObjectUtil.isEmpty(customerSupplier)) {
//                return R.fail("该供应商不存在，请修改后重试");
//            }
//        }

        // 根据计量单位id，查询计量单位对象，如果为空，提示，计量单位不存在，请修改后重试
        CommodityUnit commodityUnit = commodityUnitService.getOne(Wrappers.<CommodityUnit>lambdaQuery().eq(CommodityUnit::getId, commodityListDTO.getUnitId())
                .eq(CommodityUnit::getStatus, CommodityUnitEnum.ENABLE.getCode()));
        if (ObjectUtil.isEmpty(commodityUnit)) {
            return R.fail("计量单位不存在，请修改后重试");
        }

        // 如果点击上架，status为1，否则，默认下架状态，保存status为0
        if (!CommodityEnum.UP_SHELF.getCode().equals(commodityListDTO.getStatus())) {
            commodityListDTO.setStatus(CommodityEnum.DOWN_SHELF.getCode());
        }

        // 生成商品编号
        commodityListDTO.setNo(CodeUtil.generateCode(CodeEnum.COMMODITY_CODE));

        return R.status(commodityListService.commodityDTOSave(commodityListDTO));
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入commodityList")
    public R<?> update(@Valid @RequestBody CommodityListDTO commodityListDTO) {
        // 如果有1项参数为空，提示，参数为空，请修改后重试
        if (!ObjectUtil.isAllNotEmpty(commodityListDTO, commodityListDTO.getId(), commodityListDTO.getName(), commodityListDTO.getCatalogueId(),
                commodityListDTO.getSupplierId(), commodityListDTO.getUnitId(), commodityListDTO.getStatus())) {
            return R.fail("参数为空，请修改后重试");
        }

        // 如果附件List为空，由于当前商品只有一张图片，还需判断附件List中附件对象内容是否为空，提示，请先上传图片
        List<AttachDTO> attachList = commodityListDTO.getAttachList();
        if (CollUtil.isEmpty(attachList) || StrUtil.isBlank(attachList.get(0).getAttachId())) {
            return R.fail("请先上传图片");
        }

        // 如果商品规格List为空，提示，商品规格列表不能为空，请重新输入
        List<CommoditySpec> commoditySpecList = commodityListDTO.getCommoditySpecList();
        if (CollUtil.isEmpty(commoditySpecList)) {
            return R.fail("商品规格列表为空，请修改后重试");
        }

        // 提取商品规格List中的规格对象中的规格名字，存入到Set<String>中
        Set<String> collectSet = commoditySpecList.stream().map(CommoditySpec::getCommoditySpec).collect(Collectors.toSet());
        // 如果commoditySpecList集合中元素大于等于2，需要判断，商品规格名是否重复
        if (commoditySpecList.size() >= 2 && commoditySpecList.size() > collectSet.size()) {
            return R.fail("商品规格列表中，存在多个相同名称的规格型号");
        }
        // 遍历商品规格List
        for (CommoditySpec commoditySpec : commoditySpecList) {

            // 如果商品规格List中的规格对象，或者对象中数据为空，提示，商品规格列表中有数据为空，请重新输入
            if (!ObjectUtil.isAllNotEmpty(commoditySpec, commoditySpec.getCommoditySpec(), commoditySpec.getCommodityMinPrice(), commoditySpec.getCommodityMaxPrice())) {
                return R.fail("商品规格列表中有数据为空，请修改后重试");
            }

            // 如果商品规格List中的规格对象中的主键id为空，说明是新添加的规格数据，需要根据规格名称，查询规格对象
            if (ObjectUtil.isNotEmpty(commoditySpec) && ObjectUtil.isEmpty(commoditySpec.getId())) {
                CommoditySpec commoditySpec1 = commoditySpecService.getOne(Wrappers.<CommoditySpec>lambdaQuery().eq(CommoditySpec::getCommoditySpec, commoditySpec.getCommoditySpec()));
                // 如果不为空，并且查询得到的商品id不等于dto的id，提示，新添加的规格型号已存在，请修改后重试
                if (ObjectUtil.isNotEmpty(commoditySpec1) && !commoditySpec1.getCommodityListId().equals(commodityListDTO.getId())) {
                    return R.fail("新添加的规格型号已存在，请修改后重试");
                }
            }

            // 如果最小单价小于0，最大单价小于等于0，或者最小单价高于最大单价，提示，最小单价、最大单价为0，或者最小单价不能高于最大单价
            if (commoditySpec.getCommodityMinPrice().compareTo(BigDecimal.ZERO) < 0 ||
                    commoditySpec.getCommodityMaxPrice().compareTo(BigDecimal.ZERO) <= 0 ||
                    commoditySpec.getCommodityMinPrice().compareTo(commoditySpec.getCommodityMaxPrice()) >= 0) {
                return R.fail("最小单价、最大单价为0，或者最小单价不能高于最大单价");
            }
        }

        // 根据商品分类id，查询未删除的商品分类对象，如果为空，提示，该商品分类不存在，请修改后重试
        CommodityCatalogue commodityCatalogue = commodityCatalogueService.getOne(Wrappers.<CommodityCatalogue>lambdaQuery().eq(CommodityCatalogue::getId, commodityListDTO.getCatalogueId())
                .eq(CommodityCatalogue::getStatus, CommodityCatalogueEnum.ENABLE.getCode()));
        if (ObjectUtil.isEmpty(commodityCatalogue)) {
            return R.fail("该商品分类不存在，请修改后重试");
        }

//        // 根据供应商id，查询未删除的供应商对象，如果为空，提示，该供应商不存在，请修改后重试
//        CustomerSupplier customerSupplier = customerSupplierService.getOne(commodityListDTO.getSupplierId(), CommonConstant.OPENSTATUS).getData();
//        if (ObjectUtil.isEmpty(customerSupplier)) {
//            return R.fail("该供应商不存在，请修改后重试");
//        }

        // 根据计量单位id，查询计量单位对象，如果为空，提示，计量单位不存在，请修改后重试
        CommodityUnit commodityUnit = commodityUnitService.getOne(Wrappers.<CommodityUnit>lambdaQuery().eq(CommodityUnit::getId, commodityListDTO.getUnitId())
                .eq(CommodityUnit::getStatus, CommodityUnitEnum.ENABLE.getCode()));
        if (ObjectUtil.isEmpty(commodityUnit)) {
            return R.fail("计量单位不存在，请修改后重试");
        }

        // 根据前端传过来的商品id，查询商品对象，如果为空，提示，该id对应的商品不存在，修改操作终止
        CommodityList commodityList = commodityListService.getById(commodityListDTO);
        if (ObjectUtil.isEmpty(commodityList)) {
            return R.fail("该id对应的商品不存在，修改操作终止");
        }

        // 根据商品名称，查询商品对象（启用/禁用），如果不为空，并且其id跟前端传过来的主键id不同，说明修改后的商品名称已存在，提交时，提示，修改后的商品名称已经存在，请修改后重试
        CommodityList commodityList1 = commodityListService.getOne(Wrappers.<CommodityList>lambdaQuery().eq(CommodityList::getName, commodityListDTO.getName()));
        if (ObjectUtil.isNotEmpty(commodityList1) && !commodityList1.getId().equals(commodityListDTO.getId())) {
            return R.fail("修改后的商品名称已经存在，请修改后重试");
        }
        Assert.isTrue(StringUtil.isNotBlank(commodityListDTO.getType()), "商品业务类型不能为空，请修改后重试");
        // 如果点击上架，status为1，否则，默认下架状态，保存status为0
        if (!CommodityEnum.UP_SHELF.getCode().equals(commodityListDTO.getStatus())) {
            commodityListDTO.setStatus(CommodityEnum.DOWN_SHELF.getCode());
        }

        // 如果commodityList1不为空，并且其id跟前端传过来的主键id相同，说明该商品名称未修改，修改了其他项，可正常提交
        // 如果commodityList1为空，说明该商品名，数据库中不存在，可以正常提交
        // 更新商品和对应规格信息
        return R.status(commodityListService.updateCommodityAndSpec(commodityListDTO));
    }

    /**
     * 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入commodityList")
    public R<?> submit(@Valid @RequestBody CommodityList commodityList) {
        return R.status(commodityListService.saveOrUpdate(commodityList));
    }

    /**
     * 单个、批量删除，通用接口方法
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R<?> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        // 如果ids为空，空格，空字符串，返回参数校验失败
        if (StrUtil.isBlank(ids)) {
            return R.fail("参数ids校验失败，删除失败");
        }

        // 将String ids转为List<Long>集合
        List<Long> allIds = Func.toLongList(ids);

        // 根据前端传过来的ids列表，查询商品表，有1个为上架状态，提示，请先将商品下架再删除
        List<CommodityList> hasShelf = commodityListService.lambdaQuery().in(CollUtil.isNotEmpty(allIds), CommodityList::getId, allIds).eq(CommodityList::getStatus, CommodityEnum.UP_SHELF.getCode()).list();
        if (CollUtil.isNotEmpty(hasShelf)) {
            return R.fail("请先将商品下架再删除");
        }

        // 根据商品ids列表，查询商品白名单中间表（启用/禁用，都要查），如果不为空，提示，该商品关联了商品白名单，删除失败
        List<CommodityWhiteListCommodity> list = commodityWhiteListCommodityService.list(Wrappers.<CommodityWhiteListCommodity>lambdaQuery().in(CommodityWhiteListCommodity::getCommodityId, allIds));
        if (CollUtil.isNotEmpty(list)) {
            return R.fail("该商品关联了商品白名单，删除失败");
        }

        // 如果删除失败，提示，删除失败
        boolean result1 = commodityListService.deleteLogic(Func.toLongList(ids));
        if (!result1) {
            return R.fail("删除失败");
        }

        // 如果删除成功，需要将商品规格表中关联的数据删除
        // 方案一，存在问题：启用状态的规格数据也会被逻辑删除
        //commoditySpecService.remove(Wrappers.<CommoditySpec>lambdaQuery().in(CommoditySpec::getCommodityListId, allIds));

        // 方案二，使用更新操作，将数据设置为删除状态，同时状态设为禁用状态
        boolean result2 = commoditySpecService.update(Wrappers.<CommoditySpec>lambdaUpdate().in(CommoditySpec::getCommodityListId, allIds).
                set(CommoditySpec::getStatus, CommoditySpecEnum.DISABLE.getCode()).
                set(CommoditySpec::getIsDeleted, CommoditySpecEnum.IS_DELETE.getCode()));

        return R.status(result1 && result2);
    }

    @PostMapping("/putShelf")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "上架", notes = "单个上架")
    public R<?> putShelf(@ApiParam(value = "主键集合", required = true) @RequestParam String id) {
        return R.status(commodityListService.changeStatus(Func.toLongList(id), CommodityEnum.UP_SHELF.getCode()));
    }

    @PostMapping("/offShelf")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "下架", notes = "单个下架")
    public R<?> offShelf(@ApiParam(value = "主键集合", required = true) @RequestParam String id) {
        // 根据商品id列表，查询商品白名单中间表（启用/禁用，都要查），如果不为空，提示，该商品关联了商品白名单，下架失败
        List<CommodityWhiteListCommodity> list = commodityWhiteListCommodityService.list(Wrappers.<CommodityWhiteListCommodity>lambdaQuery().eq(CommodityWhiteListCommodity::getCommodityId, id));
        if (CollUtil.isNotEmpty(list)) {
            return R.fail("该商品关联了商品白名单，删除失败");
        }

        return R.status(commodityListService.changeStatus(Func.toLongList(id), CommodityEnum.DOWN_SHELF.getCode()));
    }

    @PostMapping("/batchShelf")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "批量上架或下架", notes = "批量上架/下架")
    public R<?> batchShelf(@ApiParam(value = "主键集合", required = true) @RequestBody OffOrPutShelfDTO offOrPutShelfDTO) {
        // 如果offOrPutShelfDTO为空，或者offOrPutShelfDTO.getIds()为空，返回参数校验错误
        if (ObjectUtil.isEmpty(offOrPutShelfDTO) || StrUtil.isBlank(offOrPutShelfDTO.getIds())) {
            return R.fail(ResultCode.PARAM_VALID_ERROR);
        }

        // 如果String ids转换为Long类型List集合为空，返回参数校验错误
        List<Long> longIds = Func.toLongList(offOrPutShelfDTO.getIds());
        if (CollUtil.isEmpty(longIds)) {
            return R.fail(ResultCode.PARAM_VALID_ERROR);
        }
//        if (offOrPutShelfDTO.getStatus().equals(0)) {
//            SpringUtil.getBean(IQualityCommodityService.class).update(Wrappers.<QualityCommodity>lambdaUpdate().in(QualityCommodity::getGoodsId, longIds).set(BaseEntity::getStatus, 1));
//        }
        return R.status(commodityListService.changeStatus(longIds, offOrPutShelfDTO.getStatus()));
    }

    @GetMapping("/getConditionSupperCommodityList")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "根据供应商查询商品信息", notes = "")
    public R<?> getConditionSupperCommodityList(String supplierId) {
        if ("undefined".equals(supplierId)) {
            return R.data(Collections.emptyList());
        }
        List<CommodityList> list = commodityListService.lambdaQuery().eq(ObjectUtil.isNotEmpty(supplierId),CommodityList::getSupplierId, supplierId)
                .eq(BaseEntity::getStatus, CommonConstant.OPENSTATUS).list();
        return R.data(list);
    }

    @GetMapping("/selectReleasePage")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "获取已上架数据", notes = "传入commodityList")
    public R<IPage<CommodityListVO>> selectReleasePage(CommodityListVO commodityList, Query query) {
        IPage<CommodityListVO> pages = commodityListService.selectPurchaseCommodityReleasePage(Condition.getPage(query), commodityList);
        return R.data(pages);
    }

    @GetMapping("/getAllUpShelfCommodityList")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "查询全部上架商品列表", notes = "不分页，只查询上架状态的商品")
    public R<List<CommodityListVO>> getAllUpShelfCommodityList() {
        // 查询所有上架状态的商品
        List<CommodityList> commodityLists = commodityListService.lambdaQuery()
                .eq(CommodityList::getStatus, CommodityEnum.UP_SHELF.getCode())
                .orderByDesc(BaseEntity::getUpdateTime)
                .list();

        // 转换为VO对象
        List<CommodityListVO> listVO = CommodityListWrapper.build().listVO(commodityLists);

        // 设置分类名称
        setCatalogueName(listVO);

        // 设置附件图标地址
        setAttachPicUrl(listVO);

        return R.data(listVO);
    }
}