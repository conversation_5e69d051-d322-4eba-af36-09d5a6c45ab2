<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>jrzh-parent</artifactId>
        <version>3.3.0-SNAPSHOT</version>
    </parent>

    <version>3.3.0-SNAPSHOT</version>
    <modules>
        <module>jrzh_manage</module>
        <module>jrzh_proxy_starter_server</module>
<!--        <module>jrzh_asset_pledge_starter_server</module>-->
        <module>jrzh_receivable_starter_server</module>
<!--        <module>jrzh_cloud_starter_server</module>-->
        <module>jrzh_order_financing_starter_server</module>
    </modules>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jrzh_platform_manage</artifactId>
    <packaging>pom</packaging>

    <repositories>
        <repository>
            <id>snapshot</id>
            <name>snapshot maven</name>
            <url>https://ceshi.jingruiit.com:18481/nexus/content/repositories/snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>release</id>
            <name>release maven</name>
            <url>https://ceshi.jingruiit.com:18481/nexus/content/groups/public/</url>
        </repository>
    </repositories>

</project>
