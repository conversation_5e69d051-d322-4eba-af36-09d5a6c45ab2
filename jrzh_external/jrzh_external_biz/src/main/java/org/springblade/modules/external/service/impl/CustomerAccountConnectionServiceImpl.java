package org.springblade.modules.external.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.bank.entity.Bank;
import org.springblade.bank.service.IBankService;
import org.springblade.common.enums.*;
import org.springblade.common.handler.CustomerRegisterHandler;
import org.springblade.common.utils.CodeUtil;
import org.springblade.common.utils.StreamUtil;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.jwt.JwtUtil;
import org.springblade.core.jwt.props.JwtProperties;
import org.springblade.core.launch.constant.TokenConstant;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.TenantBroker;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.SpringUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.customer.businessinfo.service.ICustomerBusinessInfoService;
import org.springblade.customer.constant.AuthStatus;
import org.springblade.customer.constant.SSQPersonAuthStatus;
import org.springblade.customer.entity.*;
import org.springblade.customer.enums.AuthProcess;
import org.springblade.customer.service.*;
import org.springblade.customer.util.CustomerTokenUtil;
import org.springblade.customer.vo.CustomerVO;
import org.springblade.customer.wrapper.CustomerWrapper;
import org.springblade.modules.contract.entity.ContractConfig;
import org.springblade.modules.contract.entity.ContractSignSeal;
import org.springblade.modules.contract.service.IContractConfigService;
import org.springblade.modules.contract.service.IContractSignSealService;
import org.springblade.modules.external.dto.CustomerAccountConnectionDTO;
import org.springblade.modules.external.dto.OrderInfoConnectionDTO;
import org.springblade.modules.external.entity.FieldRelate;
import org.springblade.modules.external.enums.BusinessFieldsTypeEnum;
import org.springblade.modules.external.service.ICustomerAccountConnectionService;
import org.springblade.modules.external.service.IFieldRelateService;
import org.springblade.modules.external.util.SignUtil;
import org.springblade.multifunding.entity.MultiFundingAffiliatedProducts;
import org.springblade.multifunding.service.IMultiFundingAffiliatedProductsService;
import org.springblade.othersapi.sky.dto.SupplierBusinessInfo;
import org.springblade.plan.entity.TradingOrderData;
import org.springblade.plan.enums.SystemTypeEnum;
import org.springblade.plan.enums.TradingOrderScenarioTypeEnum;
import org.springblade.plan.enums.TradingOrderStatusEnum;
import org.springblade.plan.service.IFinancingPlanBasicService;
import org.springblade.plan.service.ITradingOrderDataService;
import org.springblade.resource.builder.oss.OssBuilder;
import org.springblade.resource.entity.Attach;
import org.springblade.resource.entity.Param;
import org.springblade.resource.service.IAttachService;
import org.springblade.resource.service.IParamService;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.service.IUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户账号打通 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Slf4j
@Service
@AllArgsConstructor
public class CustomerAccountConnectionServiceImpl implements ICustomerAccountConnectionService {
    private final ICustomerService customerService;
    private final ICustomerFrontUserTypeService customerFrontUserTypeService;
    private final IContractConfigService contractConfigService;
    private final ICustomerPersonInfoService customerPersonInfoService;
    private final ICustomerInfoService customerInfoService;
    private final IContractSignSealService contractSignSealService;
    private final List<CustomerRegisterHandler> customerRegisterHandlers;
    private final ICustomerSupplierService customerSupplierService;
    private final IFieldRelateService fieldRelateService;
    private final IAttachService attachService;
    private final ICustomerBusinessInfoService businessInfoService;
    private final IParamService paramService;
    private final RemoteDeptSearchService remoteDeptSearchService;
    private final JwtProperties jwtProperties;
    private final IUserService userService;
    private final ITradingOrderDataService tradingOrderDataService;
    private final BladeRedis bladeRedis;
    private final IFinancingPlanBasicService financingPlanBasicService;
    private final ICustomerGoodsService customerGoodsService;
    private final IMultiFundingAffiliatedProductsService multiFundingAffiliatedProductsService;
    private final IEnterpriseQuotaService enterpriseQuotaService;
    private final IBankService bankService;
    private final OssBuilder ossBuilder;

    /**
     * 新增系统账号
     *
     * @param connectionDtoStr
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R postCustomerAccountConnection(String connectionDtoStr) {
        String decrypt = SignUtil.responseDecrypt(connectionDtoStr);
        JSONObject jsonObject = JSONUtil.parseObj(decrypt);
        CustomerAccountConnectionDTO connectionDTO = JSONUtil.toBean(jsonObject, CustomerAccountConnectionDTO.class);
        connectionDTO.setTenantId("000000");
        //参数校验
        checkParams(connectionDTO);
        //是否已在系统实名过
        Map<String, Object> isAuth = checkIsAuth(connectionDTO);
        //平台账号注册
        CustomerVO customerVO = customerRegister(connectionDTO, (CustomerPersonInfo) isAuth.get("personAuth"));
        //个人实名数据补充
        commitPersonAuth(connectionDTO, customerVO.getUserId(), (CustomerPersonInfo) isAuth.get("personAuth"));
        //企业实名数据补充
        commitEntAuth(connectionDTO, customerVO.getId(), (CustomerInfo) isAuth.get("entAuth"));
        //保存附件
        List<Attach> attachList = connectionDTO.getAttachList();
        attachService.saveOrUpdateBatch(attachList);
        //生成token
//        Kv authCustomer = this.getToken(customerVO.getId(), companyUserId);
        //生成token
        Customer customer = customerService.getByUserId(customerVO.getUserId());
        //返回登录数据
        String uuid = UUID.randomUUID().toString();
        Long id = customer.getId();
        bladeRedis.setEx(CacheUtil.formatCacheName(uuid, true), id + uuid, 3 * 60L);
        Map<String, Object> loginData = new HashMap<>();
        CustomerFrontUserType customerFrontUserType = customerFrontUserTypeService.getOne(Wrappers.<CustomerFrontUserType>lambdaQuery()
                .eq(CustomerFrontUserType::getCustomerFrontUserId, customer.getId())
                .eq(CustomerFrontUserType::getUserId, customerVO.getUserId()));
        loginData.put("typeId", customerFrontUserType.getId());
        loginData.put("id", customer.getId());
        loginData.put("uuid", uuid);
        loginData.put("financeAccount", customerVO.getAccountUser());
        return R.data(SignUtil.requestEncrypt(JSONUtil.toJsonStr(loginData)));
    }

    private void checkParams(CustomerAccountConnectionDTO connectionDTO) {
        if (CollectionUtil.isEmpty(connectionDTO.getContractConfigList()) ||
                CollectionUtil.isEmpty(connectionDTO.getContractSignSealList()) ||
                CollectionUtil.isEmpty(connectionDTO.getAttachList())
        ) {
            throw new RuntimeException("合同配置、合同签章、附件不能为空");
        }
        if (ObjectUtil.isNull(connectionDTO.getMemberId()) || ObjectUtil.isNull(connectionDTO.getAccountUser())
                || ObjectUtil.isNull(connectionDTO.getPhone()) || ObjectUtil.isNull(connectionDTO.getPassword())) {
            throw new RuntimeException("账户信息不能为空");
        }
        if (ObjectUtil.isEmpty(connectionDTO.getCustomerPersonInfo()) || ObjectUtil.isEmpty(connectionDTO.getCustomerInfo())
                || ObjectUtil.isNull(connectionDTO.getCustomerPersonInfo().getCustomerId()) || ObjectUtil.isNull(connectionDTO.getCustomerInfo().getCompanyId())) {
            throw new RuntimeException("客户、企业信息不能为空");
        }
    }

    private Map<String, Object> checkIsAuth(CustomerAccountConnectionDTO connectionDTO) {
        //返回数据
        Map<String, Object> map = new HashMap<>();
        //检查手机号是否已注册使用
        Customer customer = customerService.getOne(Wrappers.<Customer>lambdaQuery().eq(Customer::getPhone, connectionDTO.getPhone()));
        if (ObjectUtil.isNotEmpty(customer)) {
            CustomerPersonInfo existingPersonInfo = customerPersonInfoService.getByCustomerId(customer.getUserId());
            map.put("personAuth", existingPersonInfo);
        } else {
            // 检查个人客户是否已存在
            CustomerPersonInfo customerPersonInfo = connectionDTO.getCustomerPersonInfo();
            if (ObjectUtil.isNotEmpty(customerPersonInfo) && StringUtil.isNotBlank(customerPersonInfo.getIdentity())) {
                CustomerPersonInfo existingPersonInfo = customerPersonInfoService.isAuthOrAuthing(customerPersonInfo.getIdentity());
                if (ObjectUtil.isEmpty(existingPersonInfo)) {
                    map.put("personAuth", null);
                }
                map.put("personAuth", existingPersonInfo);
                log.info("个人客户已存在，身份证号：{}", customerPersonInfo.getIdentity());
            }
        }


        // 检查企业客户是否已存在
        CustomerInfo customerInfo = connectionDTO.getCustomerInfo();
        if (ObjectUtil.isNotEmpty(customerInfo) && StringUtil.isNotBlank(customerInfo.getBusinessLicenceNumber())) {
            CustomerInfo existingCustomerInfo = customerInfoService.isAuthOrAuthing(customerInfo.getBusinessLicenceNumber());
            if (ObjectUtil.isEmpty(existingCustomerInfo)) {
                map.put("entAuth", null);
            }
            map.put("entAuth", existingCustomerInfo);
            log.info("企业客户已存在，营业执照号：{}", customerInfo.getBusinessLicenceNumber());
        }
        return map;
    }

    /**
     * 平台账号注册
     */
    private CustomerVO customerRegister(CustomerAccountConnectionDTO connectionDTO, CustomerPersonInfo personAuth) {
        String tenantId = connectionDTO.getTenantId();
        customerService.isTenanId(tenantId);
        if (ObjectUtil.isNotEmpty(personAuth)) {
            Customer existCustomer = customerService.getByUserId(personAuth.getCustomerId());
            return CustomerWrapper.build().entityVO(existCustomer);
        }
        Customer customer = new Customer();
        customer.setName(connectionDTO.getCustomerPersonInfo().getName());
        customer.setPassword(connectionDTO.getPassword());
        customer.setPhone(connectionDTO.getPhone());
        //0-待实名 1-已实名
        customer.setStatus(1);
        //融资企业新增
        customer.setType(0);
        //账号
        customer.setAccountUser(connectionDTO.getAccountUser());
        //租户ID
        customer.setTenantId(tenantId);
        return TenantBroker.applyAs(tenantId, tenantId1 -> {
            customerFrontUserTypeService.checkCustomer(customer);
//            customerFrontUserTypeService.customerRegister(customer);
            this.typeCustomerRegister(customer);
            CustomerVO customerVO = CustomerWrapper.build().entityVO(customer);
            CustomerFrontUserType one = customerFrontUserTypeService.lambdaQuery().eq(CustomerFrontUserType::getCustomerFrontUserId, customerVO.getId()).eq(CustomerFrontUserType::getType, VerificationCodeSupertube.PERSONAL.getCode()).one();
            customerVO.setTypeId(one.getId());
            return customerVO;
        });
    }

    private void typeCustomerRegister(Customer customer) {
        customerFrontUserTypeService.checkCustomer(customer);
        if (customer.getType().equals(CustomerEnum.ZERO_TYPE.getCode())) {
            //进行同步
            User user = new User();
            //租户ID
            String tenantId = customer.getTenantId();
            user.setTenantId(tenantId);
            //账号
            user.setAccount(getByAccount(UUID.randomUUID().toString().substring(0, 5)));
            //密码
            user.setPassword(UUID.randomUUID().toString());
            //设置类型
            user.setUserType(UserTypeEnum.FINANCING.getCode());
            Dept dept = getDeptByFinancingName(tenantId);
            if (Func.isEmpty(dept)) {
                throw new ServiceException("融资企业部门不存在，请联系管理员");
            }
            user.setDeptId(String.valueOf(dept.getId()));
//            user = remoteUserService.userSubmit(user, FeignConstants.FROM_IN).getData();
            user = userService.submitUser(user);
            if (ObjectUtil.isEmpty(user)) {
                throw new ServiceException("未知错误!");
            }
            FrontFinancingList frontFinancingList = new FrontFinancingList();
            frontFinancingList.setTenantId(tenantId);
            frontFinancingList.setStatus(0);
            //编码
            frontFinancingList.setCustomerCode(CodeUtil.generateCode(CodeEnum.FINANCING_CODE));
            //设置 企业ID
            frontFinancingList.setCompanyId(user.getId());
            SpringUtil.getBean(IFrontFinancingListService.class).save(frontFinancingList);
            //设置 企业ID
            customer.setCompanyId(user.getId());
            //userID
            customer.setUserId(user.getId());
            //设置默认头像
            String logoSrc = getSysLog(tenantId);
            customer.setLogoSrc(logoSrc);
            long id = IdWorker.getId();
            customer.setId(id);
            customerType(customer, user);
            SpringUtil.getBean(ICustomerService.class).save(customer);
        } else {
            SpringUtil.getBean(ICustomerService.class).save(customer);
        }
    }

    /**
     * 创建 子用户中间表,Type 类型  企业邀请用户时 类型为1 个人  用户实名企业时类型为2  企业
     */
    private void customerType(Customer customer, User user) {
        CustomerFrontUserType customerFrontUserType = new CustomerFrontUserType();
        customerFrontUserType.setType(CustomerTypeEnum.PERSONAL.getCode());
        //注册ID
        customerFrontUserType.setCustomerFrontUserId(customer.getId());
        String tenantId = customer.getTenantId();
        customerFrontUserType.setTenantId(tenantId);
        Long id = user.getId();
        //中间表设置企业ID  个人
        customerFrontUserType.setUserId(id);
        //设置用户Id  在企业用户中   用户ID 与企业ID 不同
        customerFrontUserType.setRoleUserId(id);
        customerFrontUserTypeService.save(customerFrontUserType);
        customerFrontUserTypeService.saveEnterpriseadopt(CustomerEnum.LABEL_PERSONAL.getCode(), null, id, 1);
        customerFrontUserTypeService.success(id);

    }

    /**
     * 个人实名数据补充
     */
    private void commitPersonAuth(CustomerAccountConnectionDTO connectionDTO, Long userId, CustomerPersonInfo personAuth) {
        Long oldCustomerId;
        String personSignSealAccount;
        //3.0已经存在改用户个人实名，则只保存字段关联数据；
        if (ObjectUtil.isNotEmpty(personAuth)) {
            oldCustomerId = personAuth.getCustomerId();
            personSignSealAccount = String.valueOf(userId);
        } else {
            //保存个人合同配置
            List<ContractConfig> contractConfigList = connectionDTO.getContractConfigList();
//        ContractConfig contractConfigPerson = contractConfigList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getSignId())).findFirst().orElse(new ContractConfig());
            ContractConfig contractConfigPerson = StreamUtil.findFirst(contractConfigList, e -> ObjectUtil.isNotEmpty(e.getSignId()));
            ContractConfig newContractConfigPerson = this.buildContractConfig(contractConfigPerson, userId, CustomerTypeEnum.PERSONAL.getCode());
            contractConfigService.save(newContractConfigPerson);

            //保存个人签章
            List<ContractSignSeal> signSealList = connectionDTO.getContractSignSealList();
//        ContractSignSeal signSealPerson = signSealList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getId()) && e.getId().equals(newContractConfigPerson.getSignId())).findFirst().orElse(new ContractSignSeal());
            ContractSignSeal signSealPerson = StreamUtil.findFirst(signSealList, e -> Objects.equals(e.getId(), newContractConfigPerson.getSignId()));
            ContractSignSeal newContractSignSealPerson = this.buildContractSignSeal(signSealPerson);
            contractSignSealService.save(newContractSignSealPerson);

            //保存个人客户信息
            CustomerPersonInfo customerPersonInfo = connectionDTO.getCustomerPersonInfo();
            oldCustomerId = customerPersonInfo.getCustomerId();
            customerPersonInfo.setCustomerId(userId);
            customerPersonInfo.setProcess(AuthProcess.PERSON_PROCESS.AUTH_SUCCESS.getProcess());
            customerPersonInfo.setAuthStatus(AuthStatus.PersonAuthStatus.AUTH_PASS.getStatus());
            customerPersonInfo.setId(IdWorker.getId());
            customerPersonInfoService.save(customerPersonInfo);

            //回写user
//        User user = remoteUserService.getUserById(userId, FeignConstants.FROM_IN).getData();
            User user = userService.getById(userId);
            user.setName(customerPersonInfo.getName());
            customerRegisterHandlers.forEach(e -> {
                e.updateHandler(user);
            });
            personSignSealAccount = connectionDTO.getPersonSignSealAccount();
        }

        //添加系统业务字段
        FieldRelate fieldRelatePerson = new FieldRelate();
        fieldRelatePerson.setMemberId(connectionDTO.getMemberId());
        fieldRelatePerson.setSystemType(connectionDTO.getSystemType());
        fieldRelatePerson.setBusinessType(BusinessFieldsTypeEnum.CUSTOMER_PERSON_INFO_FIELD_CUSTOMER_ID.getCode());
        fieldRelatePerson.setExternalSystemField(String.valueOf(oldCustomerId));
        fieldRelatePerson.setInternalSystemField(String.valueOf(userId));

        FieldRelate fieldRelatePersonSignAccount = new FieldRelate();
        fieldRelatePersonSignAccount.setMemberId(connectionDTO.getMemberId());
        fieldRelatePersonSignAccount.setSystemType(connectionDTO.getSystemType());
        fieldRelatePersonSignAccount.setBusinessType(BusinessFieldsTypeEnum.CUSTOMER_PERSON_INFO_SIGN_SEAL_ACCOUNT.getCode());
        fieldRelatePersonSignAccount.setExternalSystemField(personSignSealAccount);
        fieldRelatePersonSignAccount.setInternalSystemField(String.valueOf(userId));
        List<FieldRelate> fieldRelates = Arrays.asList(fieldRelatePerson, fieldRelatePersonSignAccount);
        fieldRelateService.saveBatch(fieldRelates);

    }

    /**
     * 企业实名数据补充
     */
    private Long commitEntAuth(CustomerAccountConnectionDTO connectionDTO, Long customerFrontId, CustomerInfo entAuth) {
        Long oldCompanyId;
        Long userId;
        String entSignSealAccount;
        if (ObjectUtil.isNotEmpty(entAuth)) {
            oldCompanyId = entAuth.getCompanyId();
            userId = entAuth.getCompanyId();
            entSignSealAccount = String.valueOf(userId);
        } else {
            CustomerInfo customerInfo = connectionDTO.getCustomerInfo();
            oldCompanyId = customerInfo.getCompanyId();
            //User对象id
            userId = getNewCompanyId(customerInfo.getBusinessLicenceNumber());
            //保存企业合同配置
            List<ContractConfig> contractConfigList = connectionDTO.getContractConfigList();
            ContractConfig contractConfigEnt = StreamUtil.findFirst(contractConfigList, e -> ObjectUtil.isNotEmpty(e.getSealId()));
            ContractConfig newContractConfigEnt = this.buildContractConfig(contractConfigEnt, userId, CustomerTypeEnum.ENTERPRISE.getCode());
            contractConfigService.save(newContractConfigEnt);

            //保存企业签章
            List<ContractSignSeal> signSealList = connectionDTO.getContractSignSealList();
            ContractSignSeal signSealEnt = StreamUtil.findFirst(signSealList, e -> Objects.equals(e.getId(), newContractConfigEnt.getSealId()));
            ContractSignSeal newContractSignSealEnt = this.buildContractSignSeal(signSealEnt);
            contractSignSealService.save(newContractSignSealEnt);

            //保存企业信息、工商信息、融资企业用户类型、User用户、企业实名状态，创建客户经理数据，赋予管理员权限
            customerInfo.setCompanyId(userId);
            customerInfo.setCustomerId(customerFrontId);
            customerInfo.setId(IdWorker.getId());
            customerInfo.setEntAuthType(2);
            customerInfo.setEnterStatus(AuthStatus.ENTER_STATUS.ENTER.getStatus());
            this.updateEntInfoAndUserInfo(customerInfo);
//        customerInfoService.updateEntInfoAndUserInfo(customerInfo);
            entSignSealAccount = connectionDTO.getEntSignSealAccount();
        }


        //添加系统业务字段
        FieldRelate fieldRelateEnt = new FieldRelate();
        fieldRelateEnt.setMemberId(connectionDTO.getMemberId());
        fieldRelateEnt.setSystemType(connectionDTO.getSystemType());
        fieldRelateEnt.setBusinessType(BusinessFieldsTypeEnum.CUSTOMER_INFO_FIELD_COMPANY_ID.getCode());
        fieldRelateEnt.setExternalSystemField(String.valueOf(oldCompanyId));
        fieldRelateEnt.setInternalSystemField(String.valueOf(userId));

        FieldRelate fieldRelateEntSignAccount = new FieldRelate();
        fieldRelateEntSignAccount.setMemberId(connectionDTO.getMemberId());
        fieldRelateEntSignAccount.setSystemType(connectionDTO.getSystemType());
        fieldRelateEntSignAccount.setBusinessType(BusinessFieldsTypeEnum.CUSTOMER_INFO_SIGN_SEAL_ACCOUNT.getCode());
        fieldRelateEntSignAccount.setExternalSystemField(entSignSealAccount);
        fieldRelateEntSignAccount.setInternalSystemField(String.valueOf(userId));
        List<FieldRelate> fieldRelates = Arrays.asList(fieldRelateEnt, fieldRelateEntSignAccount);
        fieldRelateService.saveBatch(fieldRelates);

        return userId;
    }

    private void updateEntInfoAndUserInfo(CustomerInfo customerInfo) {
        //拉取工商实名信息
        String corpName = customerInfo.getCorpName();
        SupplierBusinessInfo supplierBusinessInfo = businessInfoService.saveBusinessInfo(corpName);
        String base = supplierBusinessInfo.getCompanyBasicInfo().getBase();
        customerInfo.setBase(base);
        //设置实名通过
        customerInfo.setAuthStatus(SSQPersonAuthStatus.AUTH_PASS.getCode());
        Integer legalPersonFlag = customerInfo.getLegalPersonFlag();
        if (legalPersonFlag == 1) {
            customerInfo.setStartSign(2);
        }
        customerInfoService.save(customerInfo);
        //设置User用户
        String tentId = "000000";
        User user = new User();
        user.setTenantId(tentId);
        user.setId(customerInfo.getCompanyId());
        String substring = UUID.randomUUID().toString().substring(0, 10);
        String byAccount = getByAccount(substring);
        //账号
        user.setAccount(byAccount);
        //密码
        user.setPassword(UUID.randomUUID().toString());
        //企业名称
        user.setName(customerInfo.getCorpName());
        //设置企业默认头像
        String logoSrc = getSysLog(tentId);
        user.setAvatar(logoSrc);
        //设置部门id
        Dept dept = getDeptByFinancingName(tentId);
        if (Func.isNotEmpty(dept)) {
            user.setDeptId(String.valueOf(dept.getId()));
        }
        //设置 用户类型 融资企业
        user.setUserType(UserTypeEnum.FINANCING.getCode());
        FinancingRole role = customerFrontUserTypeService.initialization(user.getId());
        Long id = role.getId();
        user.setRoleId(String.valueOf(id));
//        remoteUserService.userSubmit(user, FeignConstants.FROM_IN);
        userService.submitUser(user);
        //保存type
        CustomerFrontUserType customerFrontUserType = new CustomerFrontUserType();
        customerFrontUserType.setTenantId(tentId);
        customerFrontUserType.setType(CustomerTypeEnum.ENTERPRISE.getCode());
        customerFrontUserType.setCustomerFrontUserId(customerInfo.getCustomerId());
        Long companyId1 = user.getId();
        //设置UserId 从user表获取 主要是获取角色信息,方便控制权限
        customerFrontUserType.setRoleUserId(companyId1);
        //企业ID  一个账号不能有多个相同的企业信息
        customerFrontUserType.setUserId(companyId1);
        //新增企业信息
        boolean save = customerFrontUserTypeService.save(customerFrontUserType);
        //创建客户经理数据
        FrontFinancingList financingList = new FrontFinancingList();
        financingList.setCompanyId(companyId1);
        financingList.setStatus(0);
        //编码
        financingList.setCustomerCode(CodeUtil.generateCode(CodeEnum.FINANCING_CODE));
        SpringUtil.getBean(IFrontFinancingListService.class).save(financingList);
        //保存企业实名状态
        customerFrontUserTypeService.saveEnterpriseadopt(CustomerEnum.LABEL_FINANCING.getCode(), CustomerEnum.LEGAL_PERSON.getCode(), customerInfo.getCompanyId(), customerInfo.getEntAuthType());
        customerFrontUserTypeService.success(customerInfo.getCompanyId());
    }


    /**
     * 生成一个假账号信息，为避免账号重复，选择递归查找， 方便融资企业进行工作流操作
     */
    private String getByAccount(String account) {
//        User one = remoteUserService.getUserByAccount(account, FeignConstants.FROM_IN).getData();
        User one = userService.getOne(Wrappers.<User>lambdaQuery().eq(User::getAccount, account));
        if (ObjectUtil.isNotEmpty(one)) {
            String substring = UUID.randomUUID().toString().substring(0, 5);
            return getByAccount(substring);
        }
        return account;
    }

    /**
     * 获取系统默认头像
     */
    private String getSysLog(String tentId) {
        String logoSrc = paramService.getValue("LogoSrc");
        if (StringUtil.isBlank(logoSrc)) {
            return "";
        }
        Map<String, String> map = JSONUtil.toBean(logoSrc, Map.class);
        logoSrc = map.get(BladeConstant.ADMIN_TENANT_ID);
        if (StringUtil.isNotBlank(tentId)) {
            logoSrc = map.get(tentId);
        }
        return StringUtil.isNotBlank(tentId) ? logoSrc : "";
    }

    /**
     * //设置部门id
     */
    private Dept getDeptByFinancingName(String tenantId) {
        R<Dept> resultDept = remoteDeptSearchService.getDeptByName("融资企业", tenantId);
        return resultDept.getData();
    }

    /**
     * 构造新合同配置
     */
    private ContractConfig buildContractConfig(ContractConfig contractConfig, Long userId, Integer customerType) {
        ContractConfig newContractConfig = new ContractConfig();
        newContractConfig.setSignWay(contractConfig.getSignWay());
        newContractConfig.setImgType(contractConfig.getImgType());
        newContractConfig.setStatus(contractConfig.getStatus());
        newContractConfig.setUserId(userId);
        if (Objects.equals(customerType, CustomerTypeEnum.PERSONAL.getCode())) {
            newContractConfig.setSignId(contractConfig.getSignId());
        } else {
            newContractConfig.setSealId(contractConfig.getSealId());
        }
        return newContractConfig;
    }

    /**
     * 构造新签章数据
     */
    private ContractSignSeal buildContractSignSeal(ContractSignSeal signSealPerson) {
        ContractSignSeal newContractSignSealPerson = new ContractSignSeal();
        newContractSignSealPerson.setCategory(signSealPerson.getCategory());
        newContractSignSealPerson.setAttachId(Func.toLong(signSealPerson.getAttachId()));
        newContractSignSealPerson.setGenerateLink(signSealPerson.getGenerateLink());
        return newContractSignSealPerson;
    }

    /**
     * 查询系统中是否已经存在需要入驻的客户
     *
     * @param businessLicenceNumber
     * @return
     */
    private Long getNewCompanyId(String businessLicenceNumber) {
        //查询是否存在供应商
        CustomerSupplier customerSupplier = customerSupplierService.getByUnifiedCode(businessLicenceNumber);
        if (ObjectUtil.isNotEmpty(customerSupplier)) {
            //存在进行解绑 避免跳过实名
            return customerSupplier.getId();
        }
        return IdWorker.getId();
    }

    /**
     * 获取token
     */
    @Override
    public R getToken(Long memberId) {
        FieldRelate fieldRelatePerson = fieldRelateService.getOne(Wrappers.<FieldRelate>lambdaQuery()
                .eq(FieldRelate::getMemberId, memberId)
                .eq(FieldRelate::getBusinessType, BusinessFieldsTypeEnum.CUSTOMER_PERSON_INFO_FIELD_CUSTOMER_ID.getCode()));
        FieldRelate fieldRelateEnt = fieldRelateService.getOne(Wrappers.<FieldRelate>lambdaQuery()
                .eq(FieldRelate::getMemberId, memberId)
                .eq(FieldRelate::getBusinessType, BusinessFieldsTypeEnum.CUSTOMER_INFO_FIELD_COMPANY_ID.getCode()));
        if (ObjectUtil.isNotEmpty(fieldRelatePerson) && ObjectUtil.isNotEmpty(fieldRelateEnt)) {
            User userPerson = userService.getById(fieldRelatePerson.getInternalSystemField());
            Customer customer = customerService.getByUserId(userPerson.getId());
            return R.data(this.getToken(customer.getId(), Long.valueOf(fieldRelateEnt.getInternalSystemField())));
        } else {
            return R.data(false);
        }
    }


    public Kv getToken(Long frontCustomerId, Long companyUserId) {
        CustomerFrontUserType customerFrontUserType = customerFrontUserTypeService.getOne(Wrappers.<CustomerFrontUserType>lambdaQuery()
                .eq(CustomerFrontUserType::getCustomerFrontUserId, frontCustomerId)
                .eq(CustomerFrontUserType::getUserId, companyUserId)
        );
//        CustomerVO customerTokenVO = customerFrontUserTypeService.customerSignin(customerFrontUserType.getId(), customerVO.getUserId());
        CustomerVO customerTokenVO = this.typeCustomerSignin(customerFrontUserType.getId(), frontCustomerId);
        Kv authCustomer = CustomerTokenUtil.createAuthCustomer(customerTokenVO);
        return authCustomer;
    }

    private CustomerVO typeCustomerSignin(Long typeId, Long id) {
        CustomerVO customerVO = personalCustomer(typeId, id);
        CustomerFrontUserType one1 = customerFrontUserTypeService.getOne(Wrappers.<CustomerFrontUserType>lambdaQuery()
                .eq(CustomerFrontUserType::getId, typeId));
        Long userId = one1.getUserId();
        //获取企业基础信息
//        User one = CustomerUserCache.getUserById(userId);
        User one = userService.getById(userId);
        //获取个人 信息
        EnterpriseAdopt enterpriseAdopt = SpringUtil.getBean(IEnterpriseAdoptService.class)
                .lambdaQuery().eq(EnterpriseAdopt::getUserId, userId).one();
        Integer label = enterpriseAdopt.getLabel();
        customerVO.setLabel(label);
        customerVO.setCompanyName(one.getName());
        //获取当前企业 当前人的角色信息
//        User user = CustomerUserCache.getUserById(one1.getRoleUserId());
        User user = userService.getById(one1.getRoleUserId());
        if (ObjectUtil.isNotEmpty(user)) {
            String roleId = user.getRoleId();
            String list = customerFrontUserTypeService.selectRoleName(roleId);
            customerVO.setRoleName(list);
            customerVO.setRoleId(user.getRoleId());
            String s = this.selectRoleAlias(roleId);
            customerVO.setRoleAlias(s);
        }
        //企业头像
        customerVO.setCorporationName(one.getAvatar());

        BladeUser user1 = AuthUtil.getUser();

        if (user1 != null && jwtProperties.getState()) {
            String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
            JwtUtil.removeAccessToken(user1.getTenantId(), String.valueOf(user1.getUserId()), token);
        }
        return customerVO;
    }

    public String selectRoleAlias(String roleId) {
        List<FinancingRole> list = selectRole(roleId);
        if (ObjectUtil.isEmpty(list)) {
            return "";
        }
        //获取当前用户的角色名称
        return StringUtil.join(list.stream().map(FinancingRole::getRoleAlias).toArray(), ",");
    }

    public List<FinancingRole> selectRole(String roleId) {
        if (StringUtil.isEmpty(roleId)) {
            return new ArrayList<>();
        }
        String[] split = roleId.split(",");
        List<FinancingRole> list = SpringUtil.getBean(IFinancingRoleService.class).lambdaQuery().in(FinancingRole::getId,
                (Object[]) split).list();
        if (ObjectUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    public CustomerVO personalCustomer(Long typeId, Long id) {
        Map<String, String> parameter = SpringUtil.getBean(IParamService.class).list().stream()
                .filter(o -> o.getParamKey().equals("toDemo"))
                .collect(Collectors.toMap(Param::getParamKey, Param::getParamValue, (oldVal, newVal) -> oldVal));

        CustomerFrontUserType one = customerFrontUserTypeService.getOne(Wrappers.<CustomerFrontUserType>lambdaQuery()
                .eq(CustomerFrontUserType::getId, typeId)
                .eq(CustomerFrontUserType::getCustomerFrontUserId, id));

        if (ObjectUtil.isEmpty(one)) {
            throw new ServiceException("非法操作!");
        }
        Long customerId = one.getCustomerFrontUserId();
        Customer customer = SpringUtil.getBean(ICustomerService.class).getById(customerId);
        //设置企业ID
        customer.setCompanyId(one.getUserId());
        CustomerVO customerVO = CustomerWrapper.build().entityVO(customer);
        //查询个人信息
        //TODO 接口需要重新考虑
//        User user = CustomerUserCache.getUserById(customer.getUserId());
        User user = userService.getById(customer.getUserId());
        customerVO.setUser(user);
        customerVO.setToDemo(parameter.get("toDemo"));
        Integer type = one.getType();
        if (CustomerTypeEnum.UNDER_ENTERPRISE.getCode().equals(type)) {
            //当为企业下的人员时 则为父企业userId 数据库的user_id 还是本来的不变 只是token 发生了改变
            customerVO.setUserId(one.getUserId());
        } else {
            //为个人或者企业时 user 为本身的
            customerVO.setUserId(one.getRoleUserId());
        }
        customerVO.setSubUserId(one.getRoleUserId());
        customerVO.setType(type);
        //设置个人信息userId
        customerVO.setPersonalUserId(customer.getUserId());

        String phone = customerVO.getPhone();
        String s = DesensitizedUtil.mobilePhone(phone);
        customerVO.setPhone(s);
        //存入typeId
        customerVO.setTypeId(one.getId());
        CustomerPersonInfo one1 = SpringUtil.getBean(ICustomerPersonInfoService.class)
                .getOne(Wrappers.<CustomerPersonInfo>lambdaQuery().eq(CustomerPersonInfo::getCustomerId, user.getId()));
        if (org.springblade.core.tool.utils.ObjectUtil.isEmpty(one1)) {
            return customerVO;
        }
        String identity = one1.getIdentity();

        String iden = DesensitizedUtil.idCardNum(identity, 3, 2);
        customerVO.setIdentityCard(iden);
        return customerVO;
    }

    /**
     * 订单推送
     *
     * @param signBody
     * @return
     */
    @Override
    public R orderPush(String signBody) {
        String tenantId = "000000";
        String decrypt = SignUtil.responseDecrypt(signBody);
        JSONObject jsonObject = JSONUtil.parseObj(decrypt);
        OrderInfoConnectionDTO orderInfoConnectionDTO = JSONUtil.toBean(jsonObject, OrderInfoConnectionDTO.class);
        //判断该用户是否有打通账户
        Long memberId = orderInfoConnectionDTO.getMemberId();
        FieldRelate fieldRelatePerson = fieldRelateService.getOne(Wrappers.<FieldRelate>lambdaQuery()
                .eq(FieldRelate::getMemberId, memberId)
                .eq(FieldRelate::getBusinessType, BusinessFieldsTypeEnum.CUSTOMER_PERSON_INFO_FIELD_CUSTOMER_ID.getCode()));
        FieldRelate fieldRelateEnt = fieldRelateService.getOne(Wrappers.<FieldRelate>lambdaQuery()
                .eq(FieldRelate::getMemberId, memberId)
                .eq(FieldRelate::getBusinessType, BusinessFieldsTypeEnum.CUSTOMER_INFO_FIELD_COMPANY_ID.getCode()));
        if (ObjectUtil.isEmpty(fieldRelatePerson) || ObjectUtil.isEmpty(fieldRelateEnt)) {
            return R.fail("用户信息不存在");
        }
        //生成token
        User userPerson = userService.getById(fieldRelatePerson.getInternalSystemField());
        Customer customer = customerService.getByUserId(userPerson.getId());
        //返回登录数据
        String uuid = UUID.randomUUID().toString();
        Long id = customer.getId();
        bladeRedis.setEx(CacheUtil.formatCacheName(uuid, true), id + uuid, 3 * 60L);
        Map<String, Object> loginData = new HashMap<>();
        CustomerFrontUserType customerFrontUserType = customerFrontUserTypeService.getOne(Wrappers.<CustomerFrontUserType>lambdaQuery()
                .eq(CustomerFrontUserType::getCustomerFrontUserId, customer.getId())
                .eq(CustomerFrontUserType::getUserId, Long.valueOf(fieldRelateEnt.getInternalSystemField())));
        loginData.put("typeId", customerFrontUserType.getId());
        loginData.put("id", customer.getId());
        loginData.put("uuid", uuid);
        //订单是否已存在
        TradingOrderData existOrder = tradingOrderDataService.getOne(Wrappers.<TradingOrderData>lambdaQuery().eq(TradingOrderData::getOrderNo, orderInfoConnectionDTO.getOrderNo()));
        if (ObjectUtil.isNotEmpty(existOrder)) {
            loginData.put("orderStatus", "融资使用中");
            if (TradingOrderStatusEnum.ORDER_IN_USE.getCode().equals(existOrder.getStatus())) {
                return R.data(SignUtil.requestEncrypt(JSONUtil.toJsonStr(loginData)));
            }
            //删除旧订单
            tradingOrderDataService.removeById(existOrder);
        }
        //保存订单
        CustomerInfo customerInfo = customerInfoService.getByCompanyId(Long.valueOf(fieldRelateEnt.getInternalSystemField()));
        TradingOrderData orderData = BeanUtil.copyProperties(orderInfoConnectionDTO, TradingOrderData.class);
        orderData.setUserId(customerInfo.getCompanyId());
        orderData.setCompanyId(customerInfo.getCompanyId());
        orderData.setScenarioType(TradingOrderScenarioTypeEnum.ONE_TO_MANY.getCode());
        orderData.setStatus(TradingOrderStatusEnum.ORDER_UN_USE.getCode());
        orderData.setSalesCreditCode(customerInfo.getCorpName());
        orderData.setBuyerCreditCode(customerInfo.getCorpName());
        orderData.setSource(SystemTypeEnum.B2B.getCode());
        orderData.setGoodsLogo(this.logoTransfer(orderInfoConnectionDTO.getGoodsLogo()));
        orderData.setOrderCreateTime(this.formatOrderCreateTime(orderInfoConnectionDTO.getOrderCreateTime()));


        //保存供应商
        CustomerSupplier customerSupplier = new CustomerSupplier();
        Bank bank = bankService.getOne(Wrappers.<Bank>lambdaQuery().eq(Bank::getName, orderInfoConnectionDTO.getSettlementBankAccountName()));
        if (ObjectUtil.isEmpty(bank)) {
            throw new ServiceException("供方银行名称不存在");
        }
        customerSupplier.setSupperName(orderInfoConnectionDTO.getSalesCompanyName());
        customerSupplier.setUnifiedCode(orderInfoConnectionDTO.getSalesCreditCode());
        customerSupplier.setContacts(orderInfoConnectionDTO.getSalesName());
        customerSupplier.setSupperPhone(orderInfoConnectionDTO.getSalesPhone());
        customerSupplier.setBankAccount(orderInfoConnectionDTO.getSettlementBankAccountNum());
        customerSupplier.setDepositBankId(String.valueOf(bank.getId()));
        CustomerSupplier existSupplier = customerSupplierService.getByName(orderInfoConnectionDTO.getSalesCompanyName());
        TenantBroker.runAs(tenantId, e -> {
            tradingOrderDataService.save(orderData);
            if (ObjectUtil.isEmpty(existSupplier)) {
                customerSupplierService.save(customerSupplier);
            } else if (!existSupplier.getBankAccount().equals(orderInfoConnectionDTO.getSettlementBankAccountNum())) {
                customerSupplier.setId(existSupplier.getId());
                customerSupplierService.updateById(customerSupplier);
            }
            //更新融资方案
            SpringUtil.getBean(IFinancingPlanBasicService.class).allGroupPlanRecalculate(customerInfo.getCompanyId());
        });
//        return R.data(loginData);
        return R.data(SignUtil.requestEncrypt(JSONUtil.toJsonStr(loginData)));

    }

    private String logoTransfer(String imageUrl) {
        String transferImageFromUrl = null;
        try {
            // 从URL下载图片
            URL url = new URL(imageUrl);
            InputStream inputStream = url.openStream();

            // 生成文件名
            String fileNameStr = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
            fileNameStr = fileNameStr.contains(".") ? fileNameStr.substring(fileNameStr.lastIndexOf('.')) : ".jpg";
            String fileName = IdWorker.getId() + fileNameStr;

            // 上传到OSS
            BladeFile bladeFile = ossBuilder.template().putFile(fileName, inputStream);

            // 保存到附件表
            Long attachId = buildAttach(fileName, bladeFile);
            bladeFile.setAttachId(attachId);
            transferImageFromUrl = bladeFile.getLink();
            inputStream.close();
        } catch (Exception e) {
            log.info("图片转存失败: " + e.getMessage());
        }
        return transferImageFromUrl;

    }

    private Long buildAttach(String fileName, BladeFile bladeFile) {
        Attach attach = new Attach();
        attach.setDomain(bladeFile.getDomain());
        attach.setLink(bladeFile.getLink());
        attach.setName(bladeFile.getName());
        attach.setOriginalName(fileName);
        attachService.save(attach);
        return attach.getId();
    }

    @Override
    public R orderDel(String signBody) {
        String decrypt = SignUtil.responseDecrypt(signBody);
        JSONObject jsonObject = JSONUtil.parseObj(decrypt);
        OrderInfoConnectionDTO orderInfoConnectionDTO = JSONUtil.toBean(jsonObject, OrderInfoConnectionDTO.class);
        TradingOrderData orderData = tradingOrderDataService.getOne(Wrappers.<TradingOrderData>lambdaQuery().eq(TradingOrderData::getOrderNo, orderInfoConnectionDTO.getOrderNo()));
        if (ObjectUtil.isEmpty(orderData)) {
            return R.fail("订单不存在");
        }
        Long companyId = orderData.getCompanyId();
        Map<String, Object> result = new HashMap<>();

        Boolean orderInFinancing = true;
        if (TradingOrderStatusEnum.ORDER_UN_USE.getCode().equals(orderData.getStatus())) {
            orderInFinancing = false;
            financingPlanBasicService.removeOldData(companyId, Collections.singletonList(orderData), orderData.getScenarioType());
            tradingOrderDataService.removeById(orderData);
//            financingPlanBasicService.allGroupPlanRecalculate(companyId);
        }
        result.put("orderInFinancing", orderInFinancing);
        return R.data(SignUtil.requestEncrypt(JSONUtil.toJsonStr(result)));
    }

    /**
     * 获取客户的融资产品，且状态为可融资
     */
    @Override
    public R<String> getGoodsAndQuota(String memberIdStr) {
        String memberId = SignUtil.responseDecrypt(memberIdStr);
//        String memberId = JSONUtil.toBean(decrypt,String.class);
        //判断该用户是否有同步账户
        FieldRelate fieldRelatePerson = fieldRelateService.getOne(Wrappers.<FieldRelate>lambdaQuery()
                .eq(FieldRelate::getMemberId, memberId)
                .eq(FieldRelate::getBusinessType, BusinessFieldsTypeEnum.CUSTOMER_PERSON_INFO_FIELD_CUSTOMER_ID.getCode()));
        FieldRelate fieldRelateEnt = fieldRelateService.getOne(Wrappers.<FieldRelate>lambdaQuery()
                .eq(FieldRelate::getMemberId, memberId)
                .eq(FieldRelate::getBusinessType, BusinessFieldsTypeEnum.CUSTOMER_INFO_FIELD_COMPANY_ID.getCode()));
        if (ObjectUtil.isEmpty(fieldRelatePerson) || ObjectUtil.isEmpty(fieldRelateEnt)) {
            return R.fail("融资用户信息不存在");
        }
        User userEnt = userService.getById(fieldRelateEnt.getInternalSystemField());
        // 获取融资产品列表 我的产品-客户产品
        List<CustomerGoods> customerGoodsList = customerGoodsService.getCustomerGoodsList(null, EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(), userEnt.getId());
        if (CollectionUtils.isEmpty(customerGoodsList)) {
            return R.data(null);
        }
        List<Map<String, Object>> result = new ArrayList<>();
        //设置map，存入产品名和可用额度
        customerGoodsList.forEach(customerGoods -> {
            Long groupId = customerGoods.getGroupId();
            // 融资产品关联的 资金产品列表
            List<MultiFundingAffiliatedProducts> affiliatedProducts = multiFundingAffiliatedProductsService.getAffiliatedProductIdListByGroupId(groupId);
            if (CollUtil.isEmpty(affiliatedProducts)) {
                return;
            }
            List<Long> relatedGoodsIds = CollStreamUtil.toList(affiliatedProducts, MultiFundingAffiliatedProducts::getGoodsId);
            List<CustomerGoods> cGoodsList = customerGoodsService.list(Wrappers.<CustomerGoods>lambdaQuery()
                    .eq(CustomerGoods::getEnterpriseId, userEnt.getId())
                    .in(CustomerGoods::getGoodsId, relatedGoodsIds));
            List<Long> canOpenGoodsIdList = CollStreamUtil.toList(cGoodsList, CustomerGoods::getGoodsId);
            // 资金产品 对应 额度信息列表
            BigDecimal totalAvailable = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(cGoodsList)) {
                List<EnterpriseQuota> enterpriseQuotaList = enterpriseQuotaService.getListByGoodsIdsAndEnterpriseTypeAndgEnterpriseId(canOpenGoodsIdList, EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode(), userEnt.getId());
                totalAvailable = enterpriseQuotaList.stream()
                        .map(EnterpriseQuota::getAvailableAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            Map<String, Object> map = new HashMap<>();
            map.put("goodsName", customerGoods.getGoodsName());
            map.put("totalAvailable", totalAvailable);
            map.put("applyTime", customerGoods.getCreateTime());
            result.add(map);
        });
        return R.data(SignUtil.requestEncrypt(JSONUtil.toJsonStr(result)));
    }

    /**
     * 格式化订单创建时间
     * 将"Wed Aug 13 16:00:12 CST 2025"格式转换为"2025-08-13"格式
     *
     * @param orderCreateTime 原始时间字符串
     * @return 格式化后的时间字符串
     */
    private String formatOrderCreateTime(String orderCreateTime) {
        if (StringUtil.isBlank(orderCreateTime)) {
            return null;
        }
        try {
            // 解析原始时间格式：Wed Aug 13 16:00:12 CST 2025
            SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);
            Date date = inputFormat.parse(orderCreateTime);

            // 转换为目标格式：2025-08-13
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
            return outputFormat.format(date);
        } catch (ParseException e) {
            log.error("解析订单创建时间失败，原始时间：{}", orderCreateTime, e);
            // 如果解析失败，返回当前日期
            return orderCreateTime;
        }
    }
}
